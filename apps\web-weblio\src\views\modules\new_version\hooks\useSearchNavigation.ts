import { useSearchStore } from '#/store/search';
import { useRouter } from 'vue-router';

/**
 * @description 搜索页面跳转逻辑
 */
export function useSearchNavigation() {
  const router = useRouter();
  const searchStore = useSearchStore();

  /**
   * @description 处理普通检索和高级检索的跳转
   * @param params - 包含 queryType, searchContent, logicList 的参数对象
   */
  const handleSearch = (params: {
    logicList?: any[];
    queryType: string;
    searchContent?: string;
  }) => {
    searchStore.setAll({
      selectedSearchType: params.queryType,
      searchQuery: params.searchContent,
      logicList: params.logicList,
    });
    router.push({
      path: '/search-page',
    });
  };

  /**
   * @description 跳转到高级检索页面
   */
  const goToAdvancedSearch = () => {
    router.push({ path: '/advanced-search' });
  };

  return {
    handleSearch,
    goToAdvancedSearch,
  };
}
