// SearchListHead 样式（集成到 LoggedList 中）
.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .result-count {
    color: #666;
    .highlight {
      color: #007bff;
      font-weight: bold;
    }
  }

  .list-actions {
    display: flex;
    align-items: center;
  }

  .view-switch {
    display: flex;
    margin-left: 16px;
    border: 1px solid #dcdcdc;
    border-radius: 4px;

    .switch-btn {
      padding: 4px 8px;
      cursor: pointer;
      background-color: #fff;
      color: #666;

      &.active {
        background-color: #007bff;
        color: #fff;
      }

      &:first-child {
        border-right: 1px solid #dcdcdc;
      }
    }
  }
}

.table-container {
  flex: 1;
  overflow: auto;
  min-height: 0;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.loading-indicator,
.empty-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
