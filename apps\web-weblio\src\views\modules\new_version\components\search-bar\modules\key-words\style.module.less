.container {
  display: flex;
  align-items: center;
  font-size: 14px;
  margin-top: 30px;

  @media screen and (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

.label {
  font-weight: 400;
  font-size: 16px;
  color: #ffffff;
  margin-right: 8px;
  flex-shrink: 0;
}

.tagsWrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.tag {
  min-width: 100px;
  border: 1px solid #999999;
  font-weight: 400;
  font-size: 16px;
  color: #0f569f;
  cursor: pointer;
  padding: 6px 10px;
  text-align: center;
  background-color: #ffffff;

  &:hover {
    color: rgb(22, 93, 255);
  }

  @media screen and (max-width: 480px) {
    min-width: 100px;
    font-size: 14px;
    padding: 4px 0;
  }
}
