// 搜索相关的 Pinia 状态管理
import { defineStore } from 'pinia';
import { ref } from 'vue';

// 定义搜索相关的 store
export const useSearchStore = defineStore('search', () => {
  // 当前搜索模式：'ai' 为AI搜索，'search'为普通搜索
  const searchMode = ref<'ai' | 'search'>('search');
  // 当前选中的搜索类型（如：全部、文献、专利等）
  const selectedSearchType = ref<string>('all');
  // 普通搜索的关键词
  const searchQuery = ref<string>('');
  // AI搜索的关键词
  const aiQuery = ref<string>('');
  // 是否启用深度搜索
  const deepSearchEnabled = ref<boolean>(false);
  // 初始分类（可选）
  const initCategory = ref<number | undefined>(undefined);
  // 当前详情项（如：点击的某条搜索结果）
  const currentDetailItem = ref<any>(null);
  // logicList 字段，存储逻辑列表
  const logicList = ref<any[]>([]);
  const isNewSearch = ref(false);

  // 设置初始分类
  function setInitCategory(data: number | undefined) {
    initCategory.value = data;
  }
  // 设置搜索模式
  function setSearchMode(mode: 'ai' | 'search') {
    searchMode.value = mode;
  }
  // 设置选中的搜索类型
  function setSelectedSearchType(type: string) {
    selectedSearchType.value = type;
  }
  // 设置普通搜索关键词
  function setSearchQuery(query: string) {
    searchQuery.value = query;
  }
  // 设置AI搜索关键词
  function setAiQuery(query: string) {
    aiQuery.value = query;
  }
  // 设置是否启用深度搜索
  function setDeepSearchEnabled(enabled: boolean) {
    deepSearchEnabled.value = enabled;
  }
  // 设置当前详情项
  function setCurrentDetailItem(item: any) {
    currentDetailItem.value = item;
    localStorage.setItem('currentDetailItem', JSON.stringify(item));
  }
  // 设置 logicList
  function setLogicList(list: any[]) {
    logicList.value = list;
  }
  // 新增：重置所有字段为初始值
  function reset() {
    searchMode.value = 'search';
    selectedSearchType.value = 'all';
    searchQuery.value = '';
    aiQuery.value = '';
    deepSearchEnabled.value = false;
    initCategory.value = undefined;
    // currentDetailItem.value = null;
    isNewSearch.value = false;
  }
  // 一次性设置多个字段
  function setAll(params: {
    aiQuery?: string;
    deepSearchEnabled?: boolean;
    logicList?: any[];
    searchMode?: 'ai' | 'search';
    searchQuery?: string;
    selectedSearchType?: string;
  }) {
    if (params.searchMode !== undefined) searchMode.value = params.searchMode;
    if (params.selectedSearchType !== undefined)
      selectedSearchType.value = params.selectedSearchType;
    if (params.searchQuery !== undefined)
      searchQuery.value = params.searchQuery;
    if (params.aiQuery !== undefined) aiQuery.value = params.aiQuery;
    if (params.deepSearchEnabled !== undefined)
      deepSearchEnabled.value = params.deepSearchEnabled;
    if (params.logicList !== undefined) logicList.value = params.logicList;
    isNewSearch.value = true;
  }

  // 导出 state 和操作方法
  return {
    searchMode,
    selectedSearchType,
    searchQuery,
    aiQuery,
    deepSearchEnabled,
    currentDetailItem,
    logicList,
    isNewSearch,
    setSearchMode,
    setSelectedSearchType,
    setSearchQuery,
    setAiQuery,
    setDeepSearchEnabled,
    setAll,
    setInitCategory,
    initCategory,
    setCurrentDetailItem,
    reset,
    setLogicList,
  };
});
