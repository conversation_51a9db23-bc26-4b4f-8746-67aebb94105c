<script setup lang="ts">
import type { PaginationProps } from 'tdesign-vue-next';

import { baseDownloadFile, getDictItems, getUserInfoApi } from '#/api';
import { fetchPublicKey, rsaEncrypt, setPublicKey } from '#/utils/rsa';
import { getCategorys } from '#/views/modules/tData/api.ts';
import {
  collectByClassCode,
  collectByTree,
} from '#/views/modules/tDataClassify/api.ts';
import { saveFavorites } from '#/views/modules/tPortalFavorites/api.ts';
import { $t } from '@vben/locales';
import { useUserStore } from '@vben/stores';
import {
  AddCircleIcon,
  BrowseIcon,
  CloseIcon,
  DeleteIcon,
  DownloadIcon,
  HeartIcon,
  ShareIcon,
} from 'tdesign-icons-vue-next';
import {
  Button,
  Card,
  Form,
  FormItem,
  ImageViewer,
  Input,
  Loading,
  MessagePlugin,
  Pagination,
  Radio,
  RadioGroup,
  Select,
  SelectInput,
  Space,
  TabPanel,
  Tabs,
  Tooltip,
  Tree,
  Typography,
} from 'tdesign-vue-next';
import {
  defineExpose,
  defineProps,
  onMounted,
  reactive,
  ref,
  watch,
} from 'vue';
import { useRequest } from 'vue-hooks-plus';
import { useRoute, useRouter } from 'vue-router';

import {
  advancedSearch,
  associate,
  deleteSearch,
  getLabelList,
  getSearchHisList,
  listPageByClassify,
  search,
} from '../api';

/**
 * 属性定义
 */
const props = defineProps({
  isSearchForm: {
    type: Boolean,
    default: true,
  },
  /**
   * 编辑表单组件动作句柄
   */
  chemFormulaRef: { type: Object, default: null },

  editFormRef: { type: Object, default: null },

  shareFormRef: { type: Object, default: null },

  isAdvanced: { type: Number, default: null },

  queryType: { type: String, default: null },

  queryText: { type: String, default: null },
  /**
   * 外部扩展查询字段
   */
  extendSearchObj: {
    type: Object,
    default: () => {},
  },
});

/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * reload触发时二次传入数据
   */
  tagObj: {},
  /**
   * 数据元
   */
  dataSource: [],
  /**
   * 查询展开/关闭多字段
   */
  hideQuery: false,
  /**
   * 加载状态
   */
  loading: false,
});
const initStatus = () => {
  /**
   * 默认不显示查询
   */
  state.hideQuery = false;
  /**
   * 默认不显示加载
   */
  state.loading = false;
};
const userStore = useUserStore();
watch(
  () => userStore.userInfo,
  () => {
    if (tabs.value == 1) {
      if (resourcesValue.value == 'all') {
        doSearch();
      } else {
        doSearchWithQueryType(resourcesValue.value);
      }
    } else {
      doAdvancedSearch();
    }
  },
  { deep: true },
);
/**
 * 查询表单操作句柄
 */
const searchForm = ref();

const labels = ref();
const category = ref([]);
const categorys = ref();
const labelses = ref();
const searchContent = ref();
const searchResult = ref();
const datasetResult = ref();

const isSearchResult = ref(false);
const isDatasetResult = ref(false);

/**
 * 分页参数
 */
const Paginations = {
  current: 1,
  pageSize: 20,
  total: 0,
};
const pagination: any = ref(Paginations);

const onPageSizeChange: PaginationProps['onPageSizeChange'] = (size) => {
  pagination.value.pageSize = size;
  if (tabs.value == 1) {
    if (resourcesValue.value == 'all') {
      doSearch();
    } else {
      doSearchWithQueryType(resourcesValue.value);
    }
  } else {
    doAdvancedSearch();
  }
  console.log('page-size:', size);
};
const onCurrentChange: PaginationProps['onCurrentChange'] = (
  index,
  pageInfo,
) => {
  pagination.value.current = index;
  if (tabs.value == 1) {
    if (resourcesValue.value == 'all') {
      doSearch();
    } else {
      doSearchWithQueryType(resourcesValue.value);
    }
  } else {
    doAdvancedSearch();
  }
  console.log(pageInfo);
};
const onChange: PaginationProps['onChange'] = (pageInfo) => {
  if (tabs.value == 1) {
    if (resourcesValue.value == 'all') {
      doSearch();
    } else {
      doSearchWithQueryType(resourcesValue.value);
    }
  } else {
    doAdvancedSearch();
  }
  console.log(pageInfo);
};
const cover = ref('/lock.png');

/**
 * 网络访问方法定义
 */
const reqRunner = {
  search: useRequest(search, {
    manual: true,
    debounceWait: 300,
    onError: () => {
      loading.value = false;
    },
    onSuccess: (res: any) => {
      loading.value = false;
      isSearchResult.value = true;
      isDatasetResult.value = false;
      const { records, total } = res;
      searchResult.value = records;
      pagination.value = {
        ...pagination.value,
        total,
      };
    },
  }),
  associateSearch: useRequest(associate, {
    manual: true,
    debounceWait: 50,
    onError: () => {},
    onSuccess: (res: any) => {
      options.value = res;
    },
  }),
  advancedSearch: useRequest(advancedSearch, {
    manual: true,
    debounceWait: 300,
    onError: () => {
      loading.value = false;
    },
    onSuccess: (res: any) => {
      loading.value = false;
      isSearchResult.value = true;
      isDatasetResult.value = false;
      const { records, total } = res;
      searchResult.value = records;
      pagination.value = {
        ...pagination.value,
        total,
      };
    },
  }),
  getSearchHisList: useRequest(getSearchHisList, {
    manual: true,
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      hisOptions.value = res;
    },
  }),
  deleteSearchHis: useRequest(deleteSearch, {
    manual: true,
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {},
  }),
  downWord: useRequest(baseDownloadFile, {
    manual: true,
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {},
  }),
  downPdf: useRequest(baseDownloadFile, {
    manual: true,
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {},
  }),
  addFavorite: useRequest(saveFavorites, {
    manual: true,
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {},
  }),
  listByTree: useRequest(collectByClassCode, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      for (const item of res) {
        const list = [];
        treeDataFormat(item, list);
        treeData.value.push(list[0]);
      }
      if (sessionStorage.getItem('tabIndex')) {
        tabs.value = JSON.parse(sessionStorage.getItem('tabIndex'));
        if (sessionStorage.getItem('category')) {
          category.value = JSON.parse(sessionStorage.getItem('category'));
        }
        if (sessionStorage.getItem('searchContent')) {
          searchContent.value = sessionStorage.getItem('searchContent');
        }
        if (tabs.value == 1) {
          if (resourcesValue.value == 'all') {
            doSearch();
          } else {
            doSearchWithQueryType(resourcesValue.value);
          }
        } else {
          doAdvancedSearch();
        }
        sessionStorage.removeItem('tabIndex');
        if (sessionStorage.getItem('category')) {
          sessionStorage.removeItem('category');
        }
        if (sessionStorage.getItem('searchContent')) {
          sessionStorage.removeItem('searchContent');
        }
      } else if (route.query.CategoryCode || route.query.isDataset) {
        category.value = getAllChildrenCode(targetNode.value);
        searchContent.value = props.queryText;
        doDatasetSearch();
      } else if (props.isAdvanced == 1) {
        tabs.value = Number.parseInt(props.isAdvanced);
        searchContent.value = props.queryText;
        doSearchWithQueryType(props.queryType);
        if (props.queryType == 'molecule') {
          resourcesValue.value = 'chem';
        }
        if (props.queryType == 'experiment') {
          resourcesValue.value = 'experiment';
        }
        if (props.queryType == 'literature') {
          resourcesValue.value = 'paper';
        }
      } else if (props.isAdvanced == 2) {
        tabs.value = Number.parseInt(props.isAdvanced);
        searchContent.value = props.queryText;
        doAdvancedSearch();
      } else {
        reload();
        category.value = [];
      }
    },
  }),
  listAllByTree: useRequest(collectByTree, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => {},
    onSuccess: (res: any) => {
      treeData.value = [];
      for (const item of res) {
        const list = [];
        treeDataFormat(item, list);
        treeData.value.push(list[0]);
      }
      if (sessionStorage.getItem('tabIndex')) {
        tabs.value = JSON.parse(sessionStorage.getItem('tabIndex'));
        if (sessionStorage.getItem('category')) {
          category.value = JSON.parse(sessionStorage.getItem('category'));
        }
        if (sessionStorage.getItem('searchContent')) {
          searchContent.value = sessionStorage.getItem('searchContent');
        }
        if (tabs.value == 1) {
          if (resourcesValue.value == 'all') {
            doSearch();
          } else {
            doSearchWithQueryType(resourcesValue.value);
          }
        } else {
          doAdvancedSearch();
        }
        sessionStorage.removeItem('tabIndex');
        if (sessionStorage.getItem('category')) {
          sessionStorage.removeItem('category');
        }
        if (sessionStorage.getItem('searchContent')) {
          sessionStorage.removeItem('searchContent');
        }
      } else if (route.query.CategoryCode || route.query.isDataset) {
        category.value = getAllChildrenCode(targetNode.value);
        doDatasetSearch();
      } else if (props.isAdvanced == 1) {
        tabs.value = Number.parseInt(props.isAdvanced);
        searchContent.value = props.queryText;
        doSearchWithQueryType(props.queryType);
        if (props.queryType == 'molecule') {
          resourcesValue.value = 'chem';
        }
        if (props.queryType == 'experiment') {
          resourcesValue.value = 'experiment';
        }
        if (props.queryType == 'literature') {
          resourcesValue.value = 'paper';
        }
      } else if (props.isAdvanced == 2) {
        tabs.value = Number.parseInt(props.isAdvanced);
        doAdvancedSearch();
      } else {
        reload();
        category.value = [];
      }
    },
  }),
  datasetSearch: useRequest(listPageByClassify, {
    manual: true,
    debounceWait: 300,
    onError: () => {
      loading.value = false;
    },
    onSuccess: (res: any) => {
      loading.value = false;
      isSearchResult.value = false;
      isDatasetResult.value = true;
      const { records, total } = res;
      datasetResult.value = records;
      pagination.value = {
        ...pagination.value,
        total,
      };
    },
  }),
};
const targetNode = ref();
const treeDataFormat = (node, list) => {
  const obj = { value: '', label: '', children: [] };
  obj.value = node.classifyCode;
  obj.label =
    node.num == 0 ? node.classifyName : `${node.classifyName} (${node.num})`;
  if (node.children) {
    for (const item of node.children) {
      treeDataFormat(item, obj.children);
    }
  }
  if (Number.parseInt(route.query.CategoryCode) == node.classifyCode) {
    targetNode.value = obj;
  }
  list.push(obj);
};
const getAllChildrenCode = (node) => {
  if (!node) return [];
  const codes = [node.value];
  if (node.children) {
    for (const item of node.children) {
      codes.push(...getAllChildrenCode(item));
    }
  }
  return codes;
};
/**
 * 重新加载数据
 * @param data
 */
const reload = (data?: any) => {
  searchContent.value = '';
  searchContent.value = props.queryText;
  if (data) {
    state.tagObj = data;
  }
  if (tabs.value == 1) {
    if (resourcesValue.value == 'all') {
      doSearch();
    } else {
      doSearchWithQueryType(resourcesValue.value);
    }
  } else {
    doAdvancedSearch();
  }
};
const route = useRoute();
const resourcesType = ref();
const resourcesType1 = ref();
const resourcesType2 = ref();
const resourcesValue = ref('all');
onMounted(async () => {
  searchContent.value = '';
  if (props.queryText) {
    searchContent.value = props.queryText;
  }
  if (props.isAdvanced) {
    tabs.value = Number.parseInt(props.isAdvanced);
  }

  loading.value = true;
  datasetCode.value = '';
  category.value = [];
  // 初始化获取公钥
  const publicKey = fetchPublicKey();
  setPublicKey(await publicKey);
  // 获取分类列表
  categorys.value = await getCategorys(2);
  logicList.value = await getDictItems('LOGIC');
  resourcesType1.value = await getDictItems('DATA_TYPE');
  resourcesType2.value = await getDictItems('UNSTRUCTURED_DATA_TYPE');
  dataTypeList.value = [...resourcesType1.value, ...resourcesType2.value];
  // resourcesType.value = await getResourcesTypeWithCount({dataTypeList: dataTypeList.value});
  chemFieldList.value = await getDictItems('CHEM_SEARCH_FIELD');
  experimentFieldList.value = await getDictItems('EXPERIMENT_SEARCH_FIELD');
  paperFieldList.value = await getDictItems('PAPER_SEARCH_FIELD');
  // 标签库
  labelses.value = getLabelList();
  if (route.query.CategoryType || route.query.CategoryType == 'all') {
    reqRunner.listAllByTree.run();
  } else {
    const param = {
      secretLevel: '',
      classifyName: '',
      classifyCode: null,
    };
    if (route.query.CategoryCode) {
      const cate = route.query.CategoryCode;
      param.classifyCode = Number.parseInt(cate);
    }
    reqRunner.listByTree.run(param);
  }
});

const formulaSearch = (data?: any) => {
  searchContent.value = data;
  if (tabs.value == 1) {
    if (resourcesValue.value == 'all') {
      doSearch();
    } else {
      doSearchWithQueryType(resourcesValue.value);
    }
  } else {
    doAdvancedSearch();
  }
};
const fomateResourcesType = (data?: any) => {
  for (const item of resourcesType2.value) {
    if (item.value == data) {
      return item.label;
    }
  }
};
const initDoSearch = () => {
  pagination.value.current = 1;
  if (tabs.value == 1) {
    if (resourcesValue.value == 'all') {
      doSearch();
    } else {
      doSearchWithQueryType(resourcesValue.value);
    }
  } else {
    doAdvancedSearch();
  }
};
const doDatasetSearch = () => {
  reqRunner.datasetSearch.run({
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    param: { categoryList: category.value?.map(String) },
  });
};
const doSearch = () => {
  reqRunner.search.run({
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    category: category.value,
    searchContent: searchContent.value,
    datasetCode: datasetCode.value,
    esIndex: import.meta.env.VITE_ES_INDEX,
  });
};
const doSearchWithQueryType = (queryType?: any) => {
  reqRunner.search.run({
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    category: category.value,
    searchContent: searchContent.value,
    datasetCode: datasetCode.value,
    queryType,
    esIndex: import.meta.env.VITE_ES_INDEX,
  });
};
const associateSearch = () => {
  reqRunner.associateSearch.run({
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    searchContent: searchContent.value,
    esIndex: import.meta.env.VITE_ES_INDEX,
  });
};

const initDoAdvancedSearch = () => {
  pagination.value.current = 1;
  doAdvancedSearch();
};
const doAdvancedSearch = () => {
  reqRunner.advancedSearch.run({
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
    labels: labels.value,
    category: category.value,
    searchContent: searchContent.value,
    logicList: searchList.value,
    datasetCode: datasetCode.value,
    esIndex: import.meta.env.VITE_ES_INDEX,
  });
};

const chemFormulaSearch = (record?: any) => {
  /**
   * 通知Form组件打开编辑窗口
   */
  props.chemFormulaRef?.open(record ? { ...record } : {});
};

const logicList = ref();
const dataTypeList = ref();
const chemFieldList = ref();
const experimentFieldList = ref();
const paperFieldList = ref();
const searchList = ref([]);
const tabs = ref(1);
const tabChange = async () => {
  reload();
};
const add = async () => {
  const obj = {
    logic: '',
    dataType: '',
    field: '',
    searchList: [],
  };
  searchList.value.push(obj);
};
const deleteSearchList = async (index) => {
  searchList.value.splice(index, 1);
};
const changeDataType = async (item) => {
  if (item.dataType == 'chem') {
    item.searchList = chemFieldList.value;
  } else if (item.dataType == 'experiment' || item.dataType == 'patent_s') {
    item.searchList = experimentFieldList.value;
  } else if (
    item.dataType == 'book' ||
    item.dataType == 'paper' ||
    item.dataType == 'patent' ||
    item.dataType == 'standard' ||
    item.dataType == 'images'
  ) {
    item.searchList = paperFieldList.value;
  } else {
    item.searchList = [];
  }
};
const router = useRouter();
const toDataDetails = (code?: any) => {
  // const oneData = await getOne(code);
  // console.log(oneData);
  sessionStorage.setItem('tabIndex', JSON.stringify(tabs.value));
  if (category.value) {
    sessionStorage.setItem('category', JSON.stringify(category.value));
  }
  if (searchContent.value) {
    sessionStorage.setItem('searchContent', searchContent.value);
  }
  router.push({ name: 'tSearchResultDetail', query: { prouteCode: code } });
};

const datasetCode = ref();
const toDataSearch = async (code?: any) => {
  datasetCode.value = code;
  // resourcesType.value = await getResourcesTypeWithCount({
  //   dataTypeList: dataTypeList.value,
  //   datasetCode: datasetCode.value,
  // });
  doSearchWithQueryType();
};

const toDataDetailsPdf = (code?: any, fileType?: any) => {
  sessionStorage.setItem('tabIndex', JSON.stringify(tabs.value));
  if (searchContent.value) {
    sessionStorage.setItem('searchContent', searchContent.value);
  }
  if (
    fileType == 'pdf' ||
    fileType == 'md' ||
    fileType == 'png' ||
    fileType == 'svg' ||
    fileType == 'jpeg' ||
    fileType == 'jpg'
  ) {
    router.push({
      name: 'tSearchPdf',
      query: { prouteCode: code, type: fileType },
    });
  } else {
    downDetailsPdf(code);
  }
};

defineExpose({ formulaSearch });

const dataAuthApplys = (record?: any) => {
  if (userStore.userInfo) {
    props.editFormRef?.open(record ? { ...record } : {});
  } else {
    MessagePlugin.warning('请先进行登录');
  }
};
const shareLink = async (baseCode?: any) => {
  const rsa_baseCode = rsaEncrypt(baseCode).replaceAll('+', '%252B');
  const user_info = await getUserInfoApi();
  const rsa_username = rsaEncrypt(user_info.username).replaceAll('+', '%252B');
  const shareurl = `${import.meta.env.VITE_FRONT_URL}/datashare?p=${rsa_baseCode}&q=${rsa_username}`;
  props.shareFormRef?.open(shareurl);
};
const shareLinkPDF = async (baseCode?: any, fileType?: any) => {
  const rsa_baseCode = rsaEncrypt(baseCode).replaceAll('+', '%252B');
  const user_info = await getUserInfoApi();
  const rsa_username = rsaEncrypt(user_info.username).replaceAll('+', '%252B');
  const shareurl = `${import.meta.env.VITE_FRONT_URL}/datasharePDF?p=${rsa_baseCode}&q=${rsa_username}&type=${fileType}`;
  props.shareFormRef?.open(shareurl);
};
const popupVisible = ref(false);
const options = ref([]);
const onPopupVisibleChange = (val) => {
  popupVisible.value = val;
};
const onInputChange = (keyword) => {
  searchContent.value = keyword;
  if (keyword.length > 1) {
    associateSearch();
  } else {
    options.value = [];
  }
};
const onOptionClick = (item) => {
  searchContent.value = item;
  popupVisible.value = false;
};
const hisOptions = ref([]);
const getSearchHis = async () => {
  if (userStore.userInfo) {
    if (searchContent.value) {
      hisOptions.value = [];
    } else {
      reqRunner.getSearchHisList.run();
    }
  }
};
const clearHis = async () => {
  hisOptions.value = [];
};
const delSearchHis = async (item, index) => {
  hisOptions.value.splice(index, 1);
  reqRunner.deleteSearchHis.run(item.id);
};
const onClearSearch = async () => {
  searchContent.value = '';
  options.value = [];
};

const downDetails = async (operationCode) => {
  const { run } = reqRunner.downWord;
  run(`/tSearch/downDetails`, {
    operationCode,
  });
};

const downDetailsPdf = async (operationCode) => {
  const { run } = reqRunner.downPdf;
  run(`/tSearch/downDetailsPdf`, {
    operationCode,
  });
};

const addFavorite = async (item) => {
  reqRunner.addFavorite.run({
    dataType: item.dataType,
    operationCode: item.baseCode,
  });
  item.isFavorite = !item.isFavorite;
};
const favorite = ref({ color: 'red' });
const noFavorite = ref({});

const treeData = ref([]);
const onTreeChange = async (vals, state) => {
  datasetCode.value = '';
  category.value = vals;
  if (searchContent.value) {
    if (tabs.value == 1) {
      if (resourcesValue.value == 'all') {
        doSearch();
      } else {
        doSearchWithQueryType(resourcesValue.value);
      }
    } else {
      doAdvancedSearch();
    }
  } else {
    doDatasetSearch();
  }
};

const onRadioChange = (checkedValues) => {
  console.log(resourcesValue.value);
  doSearchWithQueryType(checkedValues);
};
const ellipsisState = {
  row: 2,
  expandable: false,
  collapsible: false,
};
const ellipsisStateTree = {
  row: 1,
  expandable: false,
  collapsible: false,
};
const loading = ref(false);
</script>

<template>
  <Space
    :size="8"
    class="tiny-tdesign-style-patch w-full pl-[6%] pr-[6%]"
    direction="vertical"
  >
    <!--    查询表单定义区域-->
    <Card v-if="isSearchForm" class="top-card">
      <Tabs :default-value="1" v-model="tabs" @change="tabChange" theme="card">
        <TabPanel :value="1" label="基础查询">
          <Form ref="searchForm" class="w-full">
            <div class="grid w-full grid-cols-1 gap-1">
              <div style="margin-left: -100px">
                <FormItem name="search">
                  <div class="w-full">
                    <SelectInput
                      :value="searchContent"
                      v-model:input-value="searchContent"
                      :popup-visible="popupVisible"
                      allow-input
                      :popup-props="{ overlayInnerStyle: { padding: '6px' } }"
                      placeholder="请输入任意关键词进行模糊查询"
                      @input-change="onInputChange"
                      @popup-visible-change="onPopupVisibleChange"
                      clearable
                      @keydown.enter="initDoSearch"
                      @focus="getSearchHis"
                      @blur="clearHis"
                      :on-clear="onClearSearch"
                      size="large"
                    >
                      <template
                        #panel
                        v-if="
                          options.length > 0 ||
                          (!searchContent && hisOptions.length > 0)
                        "
                      >
                        <ul class="tdesign-demo__select-input-ul-autocomplete">
                          <div v-if="!searchContent">
                            <div
                              style="
                                text-align: center;
                                color: gray;
                                font-size: 15px;
                                margin-bottom: 10px;
                              "
                            >
                              搜索历史
                            </div>
                            <li
                              v-for="(item, index) in hisOptions"
                              :key="item.id"
                            >
                              <div
                                style="
                                  display: flex;
                                  justify-content: space-between;
                                  padding: 2px;
                                "
                              >
                                <div
                                  @click="
                                    () => onOptionClick(item.searchKeyword)
                                  "
                                  style="cursor: pointer; width: 80%"
                                >
                                  {{ item.searchKeyword }}
                                </div>
                                <div
                                  @click="
                                    () => onOptionClick(item.searchKeyword)
                                  "
                                  style="cursor: pointer; color: grey"
                                >
                                  {{ item.createTime }}
                                </div>
                                <div style="cursor: pointer">
                                  <CloseIcon
                                    @click="() => delSearchHis(item, index)"
                                  />
                                </div>
                              </div>
                            </li>
                          </div>
                          <div
                            v-else-if="
                              searchContent && searchContent.length > 1
                            "
                          >
                            <li
                              v-for="item in options"
                              :key="item"
                              @click="() => onOptionClick(item)"
                            >
                              {{ item }}
                            </li>
                          </div>
                        </ul>
                      </template>
                    </SelectInput>
                  </div>
                  <div class="search-buttons">
                    <Button
                      theme="primary"
                      @click="initDoSearch"
                      class="search-btn"
                    >
                      <img src="/static/images/05.png" />
                    </Button>
                  </div>
                  <Button
                    theme="success"
                    class="green-btn"
                    @click="chemFormulaSearch"
                    :title="$t('webLio.search.alt.structure')"
                  >
                    <img src="/static/images/04.png" />
                  </Button>
                </FormItem>
              </div>
            </div>
          </Form>
        </TabPanel>
        <TabPanel :value="2" label="高级查询">
          <Form ref="searchForm" class="w-full">
            <div class="grid w-full grid-cols-1 gap-1">
              <div style="background-color: #0071bc1a">
                <div style="margin-left: -100px">
                  <FormItem name="search">
                    <div class="w-full">
                      <SelectInput
                        :value="searchContent"
                        v-model:input-value="searchContent"
                        :popup-visible="popupVisible"
                        allow-input
                        :popup-props="{ overlayInnerStyle: { padding: '6px' } }"
                        placeholder="请输入任意关键词进行模糊查询或点击新增进行精确查询"
                        @input-change="onInputChange"
                        @popup-visible-change="onPopupVisibleChange"
                        clearable
                        @keydown.enter="initDoAdvancedSearch"
                        @focus="getSearchHis"
                        @blur="clearHis"
                        :on-clear="onClearSearch"
                        size="large"
                      >
                        <template
                          #panel
                          v-if="
                            options.length > 0 ||
                            (!searchContent && hisOptions.length > 0)
                          "
                        >
                          <ul
                            class="tdesign-demo__select-input-ul-autocomplete"
                          >
                            <div v-if="!searchContent">
                              <div
                                style="
                                  text-align: center;
                                  color: gray;
                                  font-size: 15px;
                                  margin-bottom: 10px;
                                "
                              >
                                搜索历史
                              </div>
                              <li
                                v-for="(item, index) in hisOptions"
                                :key="item.id"
                              >
                                <div
                                  style="
                                    display: flex;
                                    justify-content: space-between;
                                    padding: 2px;
                                  "
                                >
                                  <div
                                    @click="
                                      () => onOptionClick(item.searchKeyword)
                                    "
                                    style="cursor: pointer; width: 99%"
                                  >
                                    {{ item.searchKeyword }}
                                  </div>
                                  <div>
                                    <CloseIcon
                                      @click="() => delSearchHis(item, index)"
                                    />
                                  </div>
                                </div>
                              </li>
                            </div>
                            <div
                              v-else-if="
                                searchContent && searchContent.length > 1
                              "
                            >
                              <li
                                v-for="item in options"
                                :key="item"
                                @click="() => onOptionClick(item)"
                              >
                                {{ item }}
                              </li>
                            </div>
                          </ul>
                        </template>
                      </SelectInput>
                    </div>
                    <div class="search-buttons">
                      <Button
                        theme="primary"
                        @click="initDoAdvancedSearch"
                        class="search-btn"
                      >
                        <img src="/static/images/05.png" />
                      </Button>
                    </div>
                    <Button
                      theme="success"
                      class="green-btn"
                      @click="chemFormulaSearch"
                      :title="$t('webLio.search.alt.structure')"
                    >
                      <img src="/static/images/04.png" />
                    </Button>
                  </FormItem>
                </div>
                <div
                  style="margin-left: -100px; padding-top: 10px"
                  v-for="(item, index) in searchList"
                  :key="index"
                >
                  <FormItem>
                    <div class="w-[6%] gap-1">
                      <Select
                        v-model="item.logic"
                        :options="logicList"
                        clearable
                        placeholder="请选择"
                      />
                    </div>
                    <div class="ml-2 w-[14%] gap-1">
                      <Select
                        v-model="item.dataType"
                        :options="dataTypeList"
                        :on-change="changeDataType(item)"
                        clearable
                        placeholder="请选择"
                      />
                    </div>
                    <div class="ml-2 w-[10%] gap-1">
                      <Select
                        v-model="item.field"
                        :options="item.searchList"
                        clearable
                        placeholder="请选择"
                      />
                    </div>
                    <div class="ml-2 w-[70%] gap-1">
                      <Input
                        v-model="item.query"
                        clearable
                        placeholder="请输入内容"
                      />
                    </div>
                    <div class="ml-2">
                      <Button theme="danger" @click="deleteSearchList(index)">
                        <template #icon>
                          <DeleteIcon />
                        </template>
                      </Button>
                    </div>
                  </FormItem>
                </div>
              </div>
            </div>
          </Form>
          <div class="pt-2">
            <Button theme="primary" @click="add">
              <template #icon>
                <AddCircleIcon />
              </template>
              新增
            </Button>
          </div>
        </TabPanel>
      </Tabs>
    </Card>
    <Card>
      <Loading :loading="loading" show-overlay prevent-scroll-through indicator>
        <div
          v-if="isDatasetResult || isSearchResult"
          style="display: flex; justify-content: space-between"
        >
          <div
            class="grid w-[20%] gap-5"
            style="border: 1px solid var(--td-gray-color-3)"
          >
            <Tree
              ref="tree"
              v-model="category"
              :data="treeData"
              :expand-level="1"
              hover
              line
              checkable
              value-mode="all"
              multiple
              @change="onTreeChange"
              class="w-[94%]"
            >
              <template #label="{ node }">
                <Tooltip
                  :content="node.label"
                  :destroy-on-close="false"
                  :show-arrow="false"
                  theme="light"
                  placement="bottom"
                >
                  <Typography :ellipsis="ellipsisStateTree">
                    <div>{{ node.label }}</div>
                  </Typography>
                </Tooltip>
              </template>
            </Tree>
          </div>
          <div v-if="isDatasetResult" class="w-[78%]">
            <div class="mb-4">
              <Pagination
                v-model="pagination.current"
                v-model:page-size="pagination.pageSize"
                :total="pagination.total"
                show-jumper
                @change="onChange"
                @page-size-change="onPageSizeChange"
                @current-change="onCurrentChange"
              />
            </div>
            <div class="grid w-full grid-cols-3 gap-5">
              <div v-for="item in datasetResult">
                <Card :hover-shadow="true" class="datasetClass">
                  <div
                    class="grid w-full grid-cols-1"
                    style="height: 160px; overflow-y: auto; cursor: pointer"
                    @click="toDataSearch(item.datasetCode)"
                  >
                    <div class="num-data">共{{ item.num }}条</div>
                    <div class="title-data">{{ item.datasetName }}</div>
                    <Tooltip
                      :content="item.datasetDescribe"
                      :destroy-on-close="false"
                      :show-arrow="false"
                      theme="light"
                      placement="bottom"
                    >
                      <Typography :ellipsis="ellipsisState">
                        <div class="describe-data">
                          {{ item.datasetDescribe }}
                        </div>
                      </Typography>
                    </Tooltip>
                  </div>
                </Card>
              </div>
            </div>
          </div>
          <div v-else-if="isSearchResult" class="w-[78%]">
            <div class="resourcesType" v-if="tabs == 1">
              <div>{{ '资源类型: ' }}</div>
              <RadioGroup
                default-value="all"
                v-model="resourcesValue"
                @change="onRadioChange"
              >
                <div v-for="rt in resourcesType">
                  <Radio :value="rt.value">
                    {{ rt.label }}({{ rt.count }})
                  </Radio>
                </div>
              </RadioGroup>
            </div>
            <div class="mb-4">
              <Pagination
                v-model="pagination.current"
                v-model:page-size="pagination.pageSize"
                :total="pagination.total"
                show-jumper
                @change="onChange"
                @page-size-change="onPageSizeChange"
                @current-change="onCurrentChange"
              />
            </div>
            <div class="grid w-full grid-cols-2 gap-5">
              <div v-for="item in searchResult">
                <Card v-if="item.isVisible == 0" class="card-background">
                  <div v-if="item.infomation.data_name">
                    <div
                      class="title-data"
                      v-if="item.infomation.data_name.length < 30"
                    >
                      {{ item.infomation.data_name }}
                    </div>
                    <div class="title-data" v-else>
                      <Tooltip
                        :content="item.infomation.data_name"
                        :destroy-on-close="false"
                        :show-arrow="false"
                        theme="light"
                        placement="bottom"
                      >
                        {{ item.infomation.data_name.slice(0, 29) }}...
                      </Tooltip>
                    </div>
                  </div>
                  <div v-else>
                    <div class="title-data" v-if="item.fileName.length < 30">
                      {{ item.fileName }}
                    </div>
                    <div class="title-data" v-else>
                      <Tooltip
                        :content="item.fileName"
                        :destroy-on-close="false"
                        :show-arrow="false"
                        theme="light"
                        placement="bottom"
                      >
                        {{ item.fileName.slice(0, 29) }}...
                      </Tooltip>
                    </div>
                  </div>
                  <div class="tdesign-demo-image-viewer__base">
                    <ImageViewer :images="[cover]">
                      <template #trigger>
                        <div class="tdesign-demo-image-viewer__ui-image">
                          <img
                            alt="test"
                            :src="cover"
                            class="tdesign-demo-image-viewer__ui-image--img"
                          />
                          <div
                            class="tdesign-demo-image-viewer__ui-image--hover"
                            @click="dataAuthApplys(item)"
                          >
                            <span
                              ><BrowseIcon size="1.4em" /> 查看权限申请</span
                            >
                          </div>
                        </div>
                      </template>
                    </ImageViewer>
                  </div>
                  <template #footer>
                    <div style="text-align: right">
                      <Button variant="text" shape="square" :disabled="true">
                        <HeartIcon />
                      </Button>
                      <Button
                        variant="text"
                        shape="square"
                        :style="{ 'margin-left': '8px' }"
                        :disabled="true"
                      >
                        <DownloadIcon />
                      </Button>
                      <Button
                        variant="text"
                        shape="square"
                        :style="{ 'margin-left': '8px' }"
                        :disabled="true"
                      >
                        <ShareIcon />
                      </Button>
                    </div>
                  </template>
                </Card>
                <Card
                  v-else-if="item.isVisible == 1 && item.dataType == 'chem'"
                  class="card-background"
                  :hover-shadow="true"
                >
                  <div
                    class="title-data"
                    v-if="item.infomation.data_name.length < 30"
                    @click="toDataDetails(item.baseCode)"
                    style="cursor: pointer"
                  >
                    {{ item.infomation.data_name }}
                  </div>
                  <div
                    class="title-data"
                    v-else
                    @click="toDataDetails(item.baseCode)"
                    style="cursor: pointer"
                  >
                    <Tooltip
                      :content="item.infomation.data_name"
                      :destroy-on-close="false"
                      :show-arrow="false"
                      theme="light"
                      placement="bottom"
                    >
                      {{ item.infomation.data_name.slice(0, 29) }}...
                    </Tooltip>
                  </div>
                  <div
                    class="grid w-full grid-cols-2 gap-5"
                    style="height: 160px; overflow-y: auto; cursor: pointer"
                    @click="toDataDetails(item.baseCode)"
                  >
                    <!--                  <div v-for="row in chemFieldList">
                    <div>{{`${row.label}` }}：{{ item.infomation[row.value] }}</div>
                  </div>-->
                    <div>
                      <span class="laber-data">{{ 'cas' }}：</span>
                      <span>{{ item.infomation.cas }}</span>
                    </div>
                    <div>
                      <span class="laber-data">{{ '分子量' }}：</span>
                      <span>{{ item.infomation.molecularWeight }}</span>
                    </div>
                    <div>
                      <span class="laber-data">{{ '中文名称' }}：</span>
                      <span>{{ item.infomation.c_name }}</span>
                    </div>
                    <div>
                      <span class="laber-data">{{ '英文名称' }}：</span>
                      <span v-if="item.infomation.e_name.length < 10">{{
                        item.infomation.e_name
                      }}</span>
                      <span v-else>
                        <Tooltip
                          :content="item.infomation.e_name"
                          :destroy-on-close="false"
                          :show-arrow="false"
                          theme="light"
                          placement="bottom"
                        >
                          {{ item.infomation.e_name.slice(0, 9) }}...
                        </Tooltip>
                      </span>
                    </div>
                    <div>
                      <span class="laber-data">{{ 'SMILES' }}：</span>
                      <span v-if="item.infomation.smiles.length < 10">{{
                        item.infomation.smiles
                      }}</span>
                      <span v-else>
                        <Tooltip
                          :content="item.infomation.smiles"
                          :destroy-on-close="false"
                          :show-arrow="false"
                          theme="light"
                          placement="bottom"
                        >
                          {{ item.infomation.smiles.slice(0, 9) }}...
                        </Tooltip>
                      </span>
                    </div>
                    <div>
                      <span class="laber-data">{{ '分子式' }}：</span>
                      <span>{{ item.infomation.molecularFormula }}</span>
                    </div>
                    <div>
                      <span class="laber-data">{{ '数据属性' }}：</span>
                      <span>{{ item.dataType_text }}</span>
                    </div>
                    <div>
                      <span class="laber-data">{{ '归属分类' }}：</span>
                      <span v-if="item.categoryName.length < 10">{{
                        item.categoryName
                      }}</span>
                      <span v-else>
                        <Tooltip
                          :content="item.categoryName"
                          :destroy-on-close="false"
                          :show-arrow="false"
                          theme="light"
                          placement="bottom"
                        >
                          {{ item.categoryName.slice(0, 9) }}...
                        </Tooltip>
                      </span>
                    </div>
                  </div>
                  <template #footer>
                    <div style="text-align: right">
                      <Button
                        variant="text"
                        shape="square"
                        @click="addFavorite(item)"
                      >
                        <HeartIcon
                          :style="item.isFavorite ? favorite : noFavorite"
                        />
                      </Button>
                      <Button
                        variant="text"
                        shape="square"
                        :style="{ 'margin-left': '8px' }"
                        @click="downDetails(item.baseCode)"
                      >
                        <DownloadIcon />
                      </Button>
                      <Button
                        variant="text"
                        shape="square"
                        :style="{ 'margin-left': '8px' }"
                        @click="shareLink(item.baseCode)"
                      >
                        <ShareIcon />
                      </Button>
                    </div>
                  </template>
                </Card>
                <Card
                  v-else-if="
                    item.isVisible == 1 &&
                    (item.dataType == 'experiment' ||
                      item.dataType == 'patent_s')
                  "
                  class="card-background"
                  :hover-shadow="true"
                >
                  <div
                    class="title-data"
                    v-if="item.infomation.data_name.length < 30"
                    @click="toDataDetails(item.baseCode)"
                    style="cursor: pointer"
                  >
                    {{ item.infomation.data_name }}
                  </div>
                  <div
                    class="title-data"
                    v-else
                    @click="toDataDetails(item.baseCode)"
                    style="cursor: pointer"
                  >
                    <Tooltip
                      :content="item.infomation.data_name"
                      :destroy-on-close="false"
                      :show-arrow="false"
                      theme="light"
                      placement="bottom"
                    >
                      {{ item.infomation.data_name.slice(0, 29) }}...
                    </Tooltip>
                  </div>
                  <div
                    class="grid w-full grid-cols-1 gap-1"
                    style="height: 160px; overflow-y: auto; cursor: pointer"
                    @click="toDataDetails(item.baseCode)"
                  >
                    <div style="margin-top: 40px">
                      <span class="laber-data">{{ '数据描述' }}：</span>
                      <span v-if="item.infomation.describe.length < 30">{{
                        item.infomation.describe
                      }}</span>
                      <span v-else>
                        <Tooltip
                          :content="item.infomation.describe"
                          :destroy-on-close="false"
                          :show-arrow="false"
                          theme="light"
                          placement="bottom"
                        >
                          {{ item.infomation.describe.slice(0, 29) }}...
                        </Tooltip>
                      </span>
                    </div>
                    <div>
                      <span class="laber-data">{{ '数据属性' }}：</span>
                      <span>{{ item.dataType_text }}</span>
                    </div>
                    <div>
                      <span class="laber-data">{{ '归属分类' }}：</span>
                      <span v-if="item.categoryName.length < 30">{{
                        item.categoryName
                      }}</span>
                      <span v-else>
                        <Tooltip
                          :content="item.categoryName"
                          :destroy-on-close="false"
                          :show-arrow="false"
                          theme="light"
                          placement="bottom"
                        >
                          {{ item.categoryName.slice(0, 29) }}...
                        </Tooltip>
                      </span>
                    </div>
                  </div>
                  <template #footer>
                    <div style="text-align: right">
                      <Button
                        variant="text"
                        shape="square"
                        @click="addFavorite(item)"
                      >
                        <HeartIcon
                          :style="item.isFavorite ? favorite : noFavorite"
                        />
                      </Button>
                      <Button
                        variant="text"
                        shape="square"
                        :style="{ 'margin-left': '8px' }"
                        @click="downDetails(item.baseCode)"
                      >
                        <DownloadIcon />
                      </Button>
                      <Button
                        variant="text"
                        shape="square"
                        :style="{ 'margin-left': '8px' }"
                        @click="shareLink(item.baseCode)"
                      >
                        <ShareIcon />
                      </Button>
                    </div>
                  </template>
                </Card>
                <Card
                  v-else-if="
                    item.isVisible == 1 &&
                    resourcesType2.some((it) => it.value === item.dataType)
                  "
                  class="card-background"
                  :hover-shadow="true"
                >
                  <div
                    class="title-data"
                    v-if="item.fileName.length < 30"
                    @click="toDataDetailsPdf(item.baseCode, item.fileType)"
                    style="cursor: pointer"
                  >
                    {{ item.fileName }}
                  </div>
                  <div
                    class="title-data"
                    v-else
                    @click="toDataDetailsPdf(item.baseCode, item.fileType)"
                    style="cursor: pointer"
                  >
                    <Tooltip
                      :content="item.fileName"
                      :destroy-on-close="false"
                      :show-arrow="false"
                      theme="light"
                      placement="bottom"
                    >
                      {{ item.fileName.slice(0, 29) }}...
                    </Tooltip>
                  </div>
                  <div
                    class="grid w-full grid-cols-1 gap-1"
                    style="height: 160px; overflow-y: auto; cursor: pointer"
                    @click="toDataDetailsPdf(item.baseCode, item.fileType)"
                  >
                    <!--                  <div v-for="row in paperFieldList">
                    <div>{{`${row.label}` }}：{{ item[row.value] }}</div>
                  </div>-->
                    <div style="margin-top: 40px">
                      <span class="laber-data">{{ '数据属性' }}：</span>
                      <span>{{ fomateResourcesType(item.dataType) }}</span>
                    </div>
                    <div>
                      <span class="laber-data">{{ '归属分类' }}：</span>
                      <span v-if="item.categoryName.length < 30">{{
                        item.categoryName
                      }}</span>
                      <span v-else>
                        <Tooltip
                          :content="item.categoryName"
                          :destroy-on-close="false"
                          :show-arrow="false"
                          theme="light"
                          placement="bottom"
                        >
                          {{ item.categoryName.slice(0, 29) }}...
                        </Tooltip>
                      </span>
                    </div>
                  </div>
                  <template #footer>
                    <div style="text-align: right">
                      <Button
                        variant="text"
                        shape="square"
                        @click="addFavorite(item)"
                      >
                        <HeartIcon
                          :style="item.isFavorite ? favorite : noFavorite"
                        />
                      </Button>
                      <Button
                        variant="text"
                        shape="square"
                        :style="{ 'margin-left': '8px' }"
                        @click="downDetailsPdf(item.baseCode)"
                      >
                        <DownloadIcon />
                      </Button>
                      <Button
                        variant="text"
                        shape="square"
                        :style="{ 'margin-left': '8px' }"
                        @click="shareLinkPDF(item.baseCode, item.fileType)"
                      >
                        <ShareIcon />
                      </Button>
                    </div>
                  </template>
                </Card>
              </div>
            </div>
            <div class="mt-4" v-if="searchResult.length > 0">
              <Pagination
                v-model="pagination.current"
                v-model:page-size="pagination.pageSize"
                :total="pagination.total"
                show-jumper
                @change="onChange"
                @page-size-change="onPageSizeChange"
                @current-change="onCurrentChange"
              />
            </div>
          </div>
        </div>
      </Loading>
    </Card>
  </Space>
</template>
<style scoped>
.resourcesType {
  margin-top: 4px;
  margin-bottom: 6px;
  display: inline-flex;
  align-items: center;
  gap: 20px;
}
.tdesign-demo-image-viewer__ui-image {
  width: 100%;
  height: 100%;
  display: inline-flex;
  position: relative;
  justify-content: center;
  align-items: center;
  border-radius: var(--td-radius-small);
  overflow: hidden;
}

.tdesign-demo-image-viewer__ui-image--hover {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: var(--td-text-color-anti);
  line-height: 22px;
  transition: 0.2s;
}

.tdesign-demo-image-viewer__ui-image:hover
  .tdesign-demo-image-viewer__ui-image--hover {
  opacity: 1;
  cursor: pointer;
}

.tdesign-demo-image-viewer__ui-image--img {
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 100%;
  cursor: pointer;
  position: absolute;
}

.tdesign-demo-image-viewer__ui-image--footer {
  padding: 0 16px;
  height: 56px;
  width: 100%;
  line-height: 56px;
  font-size: 16px;
  position: absolute;
  bottom: 0;
  color: var(--td-text-color-anti);
  background-image: linear-gradient(
    0deg,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0) 100%
  );
  display: flex;
  box-sizing: border-box;
}

.tdesign-demo-image-viewer__ui-image--title {
  flex: 1;
}

.tdesign-demo-popup__reference {
  margin-left: 16px;
}

.tdesign-demo-image-viewer__ui-image--icons .tdesign-demo-icon {
  cursor: pointer;
}

.tdesign-demo-image-viewer__base {
  width: 100%;
  height: 160px;
  border-radius: var(--td-radius-medium);
}
:deep(.t-tabs) {
  background-color: transparent;
}
:deep(.t-tabs__nav--card) {
  background-color: transparent;
}
:deep(.t-tabs__nav--card.t-tabs__nav-item.t-is-active) {
  background-color: #1573f9;
  color: white;
}
:deep(.t-tabs__nav--card.t-tabs__nav-item) {
  background-color: white;
  color: #1573f9;
  border-radius: 4px;
  border-bottom: none;
}
:deep(.t-tabs__nav--card.t-tabs__nav-item.t-is-active) {
  border-color: #1573f9;
  border-radius: 4px;
}
:deep(.t-tabs__nav--card.t-tabs__nav-item:last-of-type) {
  border-radius: 4px;
  border-right: none;
}
:deep(
  .t-tabs__nav--card.t-tabs__nav-item:not(.t-is-disabled):not(
      .t-is-active
    ):hover
) {
  background-color: #3789e0;
  color: white;
}
.search-buttons {
  display: flex;
}
.search-buttons {
  height: 100%;
  z-index: 10;
}
.search-btn {
  border: none;
  background: transparent;
  padding: 0 !important;
  width: 46px;
  height: 46px;
}
.search-btn:hover {
  background: transparent;
}
.search-btn {
  border-radius: 8px 8px 8px 8px !important;
  padding: 0 !important;
  width: 41px;
  height: 41px;
  margin: 0;
  background: linear-gradient(to bottom, #50a3fb, #004ea2);
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.search-btn:hover {
  background: linear-gradient(to bottom, #4095ea, #003e92);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.search-btn:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}
.green-btn {
  border: none;
  background: #00b42a !important;
  padding: 0 !important;
  width: 41px;
  height: 41px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0 4px 4px 0 !important;
  transition: background-color 0.3s ease;
}

.green-btn:hover {
  background: #009a29 !important;
}

.search-btn img,
.green-btn img {
  width: 24px;
  height: 24px;
}
.green-btn {
  border-radius: 8px 8px 8px 8px !important;
  padding: 0 !important;
  width: 41px;
  height: 41px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  background: linear-gradient(to bottom, #02d059, #00903d);
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  margin-left: 2px;
}

.green-btn:hover {
  background: linear-gradient(to bottom, #02c050, #007f30);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.green-btn:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.search-btn img,
.green-btn img {
  width: 23px;
  height: 23px;
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
}
::v-deep .t-input {
  border: none !important;
  box-shadow: none !important;
}
::v-deep .t-input--focused {
  border: none !important;
  box-shadow: none !important;
}
::v-deep .t-input.t-size-l {
  border: none !important;
  box-shadow: none !important;
}
::v-deep .t-input__inner {
  border: none !important;
  box-shadow: none !important;
  resize: none;
}
.datasetClass {
  background-image: url('/static/images/dataset.png');
  background-size: 100% 100%;
  border: none;
}
.num-data {
  font-size: 14px;
  font-weight: 700;
  line-height: 19px;
  letter-spacing: 0;
  color: #1760c2;
  margin-bottom: 20px;
}
.title-data {
  font-size: 16px;
  font-weight: 700;
  letter-spacing: 0;
  color: #333;
  position: relative;
  margin-bottom: 20px;
}
.laber-data {
  font-size: 14px;
  font-weight: 400;
  text-align: justify;
  line-height: 22px;
  letter-spacing: 0;
  color: #888;
}
.describe-data {
  font-size: 14px;
  font-weight: 400;
  text-align: justify;
  line-height: 22px;
  letter-spacing: 0;
  color: #888;
  height: 44px;
}
.card-background {
  background-image: url('/static/images/card-background.png');
  background-size: cover;
}
.top-card {
  background-image: url('/static/images/01-background06.png');
  background-size: cover;
  background-position: center;
}
</style>
