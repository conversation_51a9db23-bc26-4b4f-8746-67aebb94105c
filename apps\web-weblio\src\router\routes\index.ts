import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { traverseTreeValues } from '@vben/utils';

import { coreRoutes, fallbackNotFoundRoute } from './core';

// 注释掉动态路由的导入
// const dynamicRouteFiles = {
//   ...import.meta.glob('./modules/**/*.ts', { eager: true }),
//   ...import.meta.glob('../../views/**/router.ts', { eager: true }),
// };

// 有需要可以自行打开注释，并创建文件夹
// const externalRouteFiles = import.meta.glob('./external/**/*.ts', { eager: true });
// const staticRouteFiles = import.meta.glob('./static/**/*.ts', { eager: true });

/** 动态路由 */
// const dynamicRoutes: RouteRecordRaw[] = mergeRouteModules(dynamicRouteFiles);
const dynamicRoutes: RouteRecordRaw[] = [];

/** 外部路由列表，访问这些页面可以不需要Layout，可能用于内嵌在别的系统(不会显示在菜单中) */
// const externalRoutes: RouteRecordRaw[] = mergeRouteModules(externalRouteFiles);
/** 不需要权限的菜单列表（会显示在菜单中） */
// const staticRoutes: RouteRecordRaw[] = mergeRouteModules(staticRouteFiles);
const staticRoutes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    name: '',
    path: '/',
    children: [
      {
        path: '/home',
        name: 'home',
        component: () => import('#/views/modules/new_version/home/<USER>'),
        meta: {
          title: '首页',
          icon: 'ion:grid-outline',
        },
      },
      {
        path: '/industry-data',
        name: 'industry-data',
        component: () =>
          import('#/views/modules/new_version/industry-data/index.vue'),
        meta: {
          title: '基础数据',
          icon: 'ion:grid-outline',
        },
      },
      {
        path: '/feature-data',
        name: 'feature-data',
        component: () =>
          import('#/views/modules/new_version/feature-data/index.vue'),
        meta: {
          title: '特色数据',
          icon: 'ion:grid-outline',
        },
      },
      {
        path: '/data-tools',
        name: 'data-tools',
        component: () =>
          import('#/views/modules/new_version/data-tools/index.vue'),
        meta: {
          title: '数据工具',
          icon: 'ion:grid-outline',
        },
      },
      {
        path: '/factory-data',
        name: 'factory-data',
        component: () =>
          import('#/views/modules/new_version/factory-data/index.vue'),
        meta: {
          title: '工厂数据监控',
          icon: 'ion:business-outline',
        },
      },
      {
        path: '/data-tools-detail',
        name: 'data-tools-detail',
        component: () =>
          import('#/views/modules/new_version/data-tools-detail/index.vue'),
        meta: {
          title: '数据工具',
          icon: 'ion:grid-outline',
        },
        children: [
          {
            path: 'analytical-recombination',
            name: 'analytical-recombination',
            component: () =>
              import(
                '#/views/modules/new_version/data-tools-detail/modules/analytical-recombination/analytical-recombination.vue'
              ),
            meta: {
              title: '全文多模态解析重组工具',
              icon: 'ion:document-text-outline',
            },
          },
          {
            path: 'relationship',
            name: 'relationship',
            component: () =>
              import(
                '#/views/modules/new_version/data-tools-detail/modules/relationship/relationship.vue'
              ),
            meta: {
              title: '知识对象及关系挖掘工具',
              icon: 'ion:git-network-outline',
            },
          },
          {
            path: 'relationship-result',
            name: 'relationship-result',
            component: () =>
              import(
                '#/views/modules/new_version/data-tools-detail/modules/relationship/relationship-result.vue'
              ),
            meta: {
              title: '知识对象及关系挖掘工具 - 对齐结果',
              icon: 'ion:analytics-outline',
            },
          },
          {
            path: 'data-cleaning',
            name: 'data-cleaning',
            component: () =>
              import(
                '#/views/modules/new_version/data-tools-detail/modules/data-cleaning/data-cleaning.vue'
              ),
            meta: {
              title: '数据汇聚与清洗工具',
              icon: 'ion:funnel-outline',
            },
          },
          {
            path: 'data-classification',
            name: 'data-classification',
            component: () =>
              import(
                '#/views/modules/new_version/data-tools-detail/modules/data-classification/data-classification.vue'
              ),
            meta: {
              title: '数据整编与分类工具',
              icon: 'ion:library-outline',
            },
          },
          {
            path: 'quality-control',
            name: 'quality-control',
            component: () =>
              import(
                '#/views/modules/new_version/data-tools-detail/modules/quality-control/quality-control.vue'
              ),
            meta: {
              title: '质量控制工具',
              icon: 'ion:checkmark-circle-outline',
            },
          },
        ],
      },
      {
        path: '/data-flow',
        name: 'data-flow',
        component: () =>
          import('#/views/modules/new_version/data-flow/index.vue'),
        meta: {
          title: '数据流通',
          icon: 'ion:grid-outline',
        },
      },
      {
        path: '/about',
        name: 'about',
        component: () => import('#/views/modules/new_version/about/index.vue'),
        meta: {
          title: '关于我们',
          icon: 'ion:grid-outline',
        },
      },
      {
        path: '/user-feedback',
        name: 'user-feedback',
        component: () => import('#/views/modules/tSysFeedback/index.vue'),
        meta: {
          title: '用户反馈',
          icon: 'ion:grid-outline',
        },
      },
      {
        path: '/me',
        name: 'me',
        component: () => import('#/views/modules/new_version/me/index.vue'),
        meta: {
          title: '个人中心',
          icon: 'ion:grid-outline',
        },
      },
      {
        path: '/search-page',
        name: 'search-page',
        component: () =>
          import('#/views/modules/new_version/search-page/index.vue'),
        meta: {
          title: '搜索',
          icon: 'ion:grid-outline',
        },
      },
      {
        path: '/advanced-search',
        name: 'advanced-search',
        component: () =>
          import('#/views/modules/new_version/advanced-search/index.vue'),
        meta: {
          title: '高级检索',
          icon: 'ion:grid-outline',
        },
      },
      {
        path: '/details',
        name: 'details',
        component: () =>
          import('#/views/modules/new_version/details/index.vue'),
        meta: {
          title: '详情',
          icon: 'ion:grid-outline',
        },
        children: [
          {
            path: 'basic-info',
            name: 'details-basic-info',
            component: () =>
              import(
                '#/views/modules/new_version/details/modules/basic-info/index.vue'
              ),
            meta: {
              title: '基础信息',
              icon: 'info-circle',
            },
          },
          {
            path: 'basic-info/:id',
            name: 'details-basic-info-detail',
            component: () =>
              import(
                '#/views/modules/new_version/details/modules/basic-info/index.vue'
              ),
            meta: {
              title: '详细信息',
              icon: 'file-text',
            },
            props: true,
          },
        ],
      },
      {
        path: '/method-codes',
        name: 'MethodCodes',
        component: () =>
          import(
            '#/views/modules/new_version/details/modules/basic-info/components/Method.vue'
          ),
        meta: {
          title: 'Method Codes for Summary Calculations',
          icon: 'file-text',
        },
      },
      {
        path: '/dwdTechnology',
        name: 'dwdTechnology',
        component: () => import('#/views/modules/dwdTechnology/index.vue'),
        meta: {
          title: '工艺一览',
          icon: 'ion:grid-outline',
        },
      },
      {
        path: '/tSysDataTools',
        name: 'tSysDataTools',
        component: () => import('#/views/modules/tSysDataTools/index.vue'),
        meta: {
          title: '数据工具',
          icon: 'ion:grid-outline',
        },
      },
      {
        path: '/user-guide',
        name: 'user-guide',
        component: () =>
          import('#/views/modules/new_version/user-guide/index.vue'),
        meta: {
          title: '用户指南',
          icon: 'ion:grid-outline',
        },
      },
      // ============================旧版本===========================
      // {
      //   path: '/tDataClassDetail',
      //   name: 'tDataClassDetail',
      //   component: () => import('../../views/modules/tDataClassify/classDetail.vue'),
      //   meta: {
      //     title: '分类汇总',
      //     icon: 'ion:grid-outline',
      //   },
      // },
      // {
      //   path: '/tDataIndex',
      //   name: 'tDataIndex',
      //   component: () => import('../../views/modules/tData/index.vue'),
      //   meta: {
      //     title: '数据填报',
      //     icon: 'ion:grid-outline',
      //   },
      // },
      // {
      //   path: '/MyInstance',
      //   name: 'MyInstance',
      //   component: () => import('../../views/_workflow/my-instance/index.vue'),
      //   meta: {
      //     title: '审批查询',
      //     icon: 'ion:grid-outline',
      //   },
      // },
      // {
      //   path: '/tSearchIndex',
      //   name: 'tSearchIndex',
      //   component: () => import('../../views/modules/tSearch/index.vue'),
      //   meta: {
      //     title: '数据搜索',
      //     icon: 'ion:grid-outline',
      //   },
      // },
      // {
      //   path: '/tSearch/tSearchResultDetail',
      //   name: 'tSearchResultDetail',
      //   component: () => import('../../views/modules/tSearch/dataDetails.vue'),
      //   meta: {
      //     title: '数据详情',
      //     icon: 'ion:grid-outline',
      //   },
      // },
      // {
      //   path: '/tSearch/tSearchPdf',
      //   name: 'tSearchPdf',
      //   component: () => import('../../views/modules/tSearch/dataDetailsPdf.vue'),
      //   meta: {
      //     title: '数据详情',
      //     icon: 'ion:grid-outline',
      //   },
      // },
      // {
      //   path: '/MyTask',
      //   name: 'MyTask',
      //   component: () => import('../../views/_workflow/my-task/index.vue'),
      //   meta: {
      //     title: '待办',
      //     icon: 'ion:grid-outline',
      //   },
      // },
      // {
      //   path: '/MyHisInstance',
      //   name: 'MyHisInstance',
      //   component: () => import('../../views/_workflow/my-his-instance/index.vue'),
      //   meta: {
      //     title: '已办',
      //     icon: 'ion:grid-outline',
      //   },
      // },
      {
        path: '/tPortalFavoritesIndex',
        name: 'tPortalFavoritesIndex',
        component: () =>
          import('../../views/modules/tPortalFavorites/index.vue'),
        meta: {
          title: '收藏夹',
          icon: 'ion:grid-outline',
        },
      },
      // {
      //   path: '/tDataUnstructuredIndex',
      //   name: 'tDataUnstructuredIndex',
      //   component: () => import('../../views/modules/tDataUnstructured/index.vue'),
      //   meta: {
      //     title: '我的文件',
      //     icon: 'ion:grid-outline',
      //   },
      // },
      // {
      //   path: '/tDataBaseIndex',
      //   name: 'tDataBaseIndex',
      //   component: () => import('../../views/modules/tDataBase/index.vue'),
      //   meta: {
      //     title: '我的数据',
      //     icon: 'ion:grid-outline',
      //   },
      // },
      // {
      //   path: '/tDataTemplateIndex',
      //   name: 'tDataTemplateIndex',
      //   component: () => import('../../views/modules/tDataTemplate/index.vue'),
      //   meta: {
      //     title: '数据模板',
      //     icon: 'ion:grid-outline',
      //   },
      // },
      // {
      //   path: '/tDataServiceIndex',
      //   name: 'tDataServiceIndex',
      //   component: () => import('../../views/modules/tDataService/index.vue'),
      //   meta: {
      //     title: '数据工具',
      //     icon: 'ion:grid-outline',
      //   },
      // },
      {
        path: '/aboutUs',
        name: 'aboutUs',
        component: () => import('../../views/modules/aboutUs/index.vue'),
        meta: {
          title: '关于我们',
          icon: 'ion:grid-outline',
        },
      },
      // {
      //   name: '/Register',
      //   path: 'register',
      //   component: () => import('#/views/_core/authentication/register.vue'),
      //   meta: {
      //     title: $t('page.auth.register'),
      //   },
      // },
    ],
  },
];
const externalRoutes: RouteRecordRaw[] = [];

/** 路由列表，由基本路由+静态路由组成 */
const routes: RouteRecordRaw[] = [
  ...coreRoutes,
  ...externalRoutes,
  fallbackNotFoundRoute,
];

/** 基本路由列表，这些路由不需要进入权限拦截 */
const coreRouteNames = traverseTreeValues(coreRoutes, (route) => route.name);

const accessRoutes = [...dynamicRoutes, ...staticRoutes];
export { accessRoutes, coreRouteNames, routes };
