// src/api/technology.ts
import { requestClient } from '#/api/request';

export async function listByPage(data: any) {
  return requestClient.post<any>('/rgdc-submit/dwdTechnologyDetails/listByPage', data);
}

// 工艺数据查看权限申请
export async function technologyDataApply(data: any) {
  return requestClient.post<any>('/rgdc-submit/tDataPermission/technologyDataApply', data);
}

// 获取筛选选项
export async function getFilterOptions() {
  return requestClient.post<any>('/rgdc-submit/dwdTechnologyDetails/getFilterOptions', {});
}
