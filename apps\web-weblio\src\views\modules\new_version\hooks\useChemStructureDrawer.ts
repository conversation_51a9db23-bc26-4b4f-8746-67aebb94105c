import { ref } from 'vue';

const isVisible = ref(false);
const structure = ref({});
const onSuccessCallback = ref<((data: any) => void) | null>(null);

export function useChemStructureDrawer() {
  const openDrawer = (onSuccess?: (data: any) => void) => {
    onSuccessCallback.value = onSuccess || null;
    isVisible.value = true;
  };

  const closeDrawer = () => {
    isVisible.value = false;
    onSuccessCallback.value = null;
  };

  const handleSuccess = (data: any) => {
    structure.value = data;
    if (onSuccessCallback.value) {
      onSuccessCallback.value(data);
    }
    closeDrawer();
    // 清空状态以便下次使用
    structure.value = {};
  };

  return {
    isVisible,
    structure,
    openDrawer,
    closeDrawer,
    handleSuccess,
  };
}
