<script lang="ts" setup>
import type { VbenFormSchema } from '@vben/common-ui';
import type { Recordable } from '@vben/types';

import { computed, h, ref, onMounted, nextTick, watch } from 'vue';

import { AuthenticationRegister, z } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';

import { registerApi, getNotification } from '#/api';
import {useRouter} from "vue-router";
import { useAuthStore } from '#/store';

defineOptions({ name: 'Register' });

const loading = ref(false);
const router = useRouter();

// 验证码相关
const smsCodeSending = ref(false);
const smsCodeCountdown = ref(0);
const smsCodeText = ref('发送验证码');
const smsCodeValue = ref('');
const authStore = useAuthStore();

// 图形验证码相关
const captchaCode = ref('');
const captchaImage = ref('');
const captchaKey = ref('');
const captchaLoading = ref(false);
const captchaError = ref('');
const showCaptchaError = ref(false);

// 表单引用
const registerFormRef = ref();
const smsButtonRef = ref();
const captchaButtonRef = ref();

// 获取图形验证码
async function getCaptcha() {
  try {
    // 开始加载
    captchaLoading.value = true;

    // 清空当前输入的验证码和图片
    captchaCode.value = '';
    captchaImage.value = '';

    // 清除验证码错误状态
    clearCaptchaError();

    // 生成验证码key
    captchaKey.value = `${new Date().getTime()}-${Math.random().toString(36).substr(2, 9)}`;

    // 调用后端API获取验证码图片
    const response = await authStore.getAuthCaptchaImage(captchaKey.value);

    // 如果返回的是base64图片数据
    captchaImage.value = response;
    captchaLoading.value = false;
  } catch (error) {
    console.error('获取验证码失败:', error);
    captchaLoading.value = false;
    MessagePlugin.error('获取验证码失败，请重试');
  }
}

// 供父组件调用的设置验证码方法
function setCaptchaData(imageData: string, key: string) {
  captchaImage.value = imageData;
  captchaKey.value = key;
  // 停止加载状态
  captchaLoading.value = false;
  // 清除验证码错误状态
  clearCaptchaError();
  // 清空用户输入的验证码
  captchaCode.value = '';
  // 同步到表单
  const formApi = registerFormRef.value?.getFormApi?.();
  if (formApi) {
    formApi.setFieldValue('captcha', '');
  }
}

// 验证图形验证码
function validateCaptcha() {
  showCaptchaError.value = false;
  captchaError.value = '';

  if (!captchaCode.value) {
    captchaError.value = '请输入图形验证码';
    showCaptchaError.value = true;
    return false;
  }

  if (captchaCode.value.length < 4) {
    captchaError.value = '图形验证码长度不正确';
    showCaptchaError.value = true;
    return false;
  }

  return true;
}

// 清除验证码错误状态
function clearCaptchaError() {
  if (showCaptchaError.value) {
    showCaptchaError.value = false;
    captchaError.value = '';
  }
}

// 发送短信验证码
async function sendSmsCode() {
  try {
    // 获取表单API
    const formApi = registerFormRef.value?.getFormApi?.();
    if (!formApi) {
      MessagePlugin.error('表单未初始化');
      return;
    }
    
    const values = await formApi.getValues();
    // 手机号
    const phoneNumber = values.phoneNumber;
    // 图形验证码
    const captchaCode = values.captcha;
    
    if (!phoneNumber) {
      MessagePlugin.error('请先输入手机号');
      return;
    }
    
    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phoneNumber)) {
      MessagePlugin.error('请输入正确的手机号格式');
      return;
    }

    // 验证图形验证码
    if (!captchaCode) {
      MessagePlugin.error('请输入图形验证码');
      return;
    }

    smsCodeSending.value = true;
    // 调用发送验证码，传递图形验证码参数
    let res = await authStore.sendSmsCode(phoneNumber, captchaCode, captchaKey.value, 'register');
    if (res != null && res != undefined && res != '' && res['result'] == 'success'){
      MessagePlugin.success('验证码已发送，请注意查收');
      // 发送成功,开始倒计时
      startCountdown();
    } else if (res != null && res != undefined && res != '' && res['result'] == 'test'){
      MessagePlugin.success('验证码已发送，请注意查收');
      smsCodeValue.value = res['smsCode'];
      
      // 填充验证码到表单
      const formApi = registerFormRef.value?.getFormApi?.();
      if (formApi) {
        await formApi.setFieldValue('smsCode', res['smsCode']);
      }
      
      // 发送成功,开始倒计时
      startCountdown();
    } else{
      MessagePlugin.error('发送验证码失败，请重试');
    }
    
  } catch (error) {
    console.error('发送验证码失败:', error);
    if (error.code !== '9901'){
      MessagePlugin.error('发送验证码失败，请重试');
    }
  } finally {
    smsCodeSending.value = false;
  }
}

// 倒计时功能
function startCountdown() {
  smsCodeCountdown.value = 60;
  smsCodeText.value = `${smsCodeCountdown.value}秒后重发`;
  
  const timer = setInterval(() => {
    smsCodeCountdown.value--;
    if (smsCodeCountdown.value > 0) {
      smsCodeText.value = `${smsCodeCountdown.value}秒后重发`;
    } else {
      smsCodeText.value = '重新发送';
      clearInterval(timer);
    }
  }, 1000);
}

// 发送按钮
function positionSmsButton() {
  nextTick(() => {
    setTimeout(() => {
      // 短信验证码输入框 - 更精确的选择器
      let smsCodeInput: HTMLInputElement | null = null;
      const inputs = document.querySelectorAll('input');
      
      for (const input of inputs) {
        if (input.placeholder && input.placeholder.includes('验证码') && !input.placeholder.includes('图形验证码')) {
          smsCodeInput = input;
          break;
        }
      }
      
      const smsButton = smsButtonRef.value as HTMLElement;
      
      if (smsCodeInput && smsButton) {
        const inputRect = smsCodeInput.getBoundingClientRect();
        const wrapperElement = document.querySelector('.register-wrapper') as HTMLElement;
        
        if (wrapperElement) {
          const wrapperRect = wrapperElement.getBoundingClientRect();
          const buttonTop = inputRect.top - wrapperRect.top + inputRect.height / 2;
          
          // 发送按钮样式
          smsButton.style.visibility = 'hidden';
          smsButton.style.position = 'absolute';
          smsButton.style.top = `${buttonTop}px`;
          smsButton.style.right = '8px';
          smsButton.style.transform = 'translateY(-50%)';
          smsButton.style.zIndex = '10';
          smsButton.style.display = 'flex';
          smsButton.style.alignItems = 'center';
          smsButton.style.justifyContent = 'center';
          
          smsCodeInput.style.paddingRight = '110px';
          
          // 展示发送按钮
          setTimeout(() => {
            smsButton.style.visibility = 'visible';
          }, 50);
        }
      }
    }, 0);
  });
}

// 图形验证码按钮定位
function positionCaptchaButton() {
  nextTick(() => {
    setTimeout(() => {
      // 图形验证码输入框
      let captchaInput: HTMLInputElement | null = null;
      const inputs = document.querySelectorAll('input');
      
      for (const input of inputs) {
        if (input.placeholder && input.placeholder.includes('图形验证码')) {
          captchaInput = input;
          break;
        }
      }
      
      const captchaButton = captchaButtonRef.value as HTMLElement;
      
      if (captchaInput && captchaButton) {
        const inputRect = captchaInput.getBoundingClientRect();
        const wrapperElement = document.querySelector('.register-wrapper') as HTMLElement;
        
        if (wrapperElement) {
          const wrapperRect = wrapperElement.getBoundingClientRect();
          const buttonTop = inputRect.top - wrapperRect.top + inputRect.height / 2;
          
          // 图形验证码按钮样式
          captchaButton.style.visibility = 'hidden';
          captchaButton.style.position = 'absolute';
          captchaButton.style.top = `${buttonTop}px`;
          captchaButton.style.right = '8px';
          captchaButton.style.transform = 'translateY(-50%)';
          captchaButton.style.zIndex = '10';
          captchaButton.style.display = 'flex';
          captchaButton.style.alignItems = 'center';
          captchaButton.style.justifyContent = 'center';
          captchaButton.style.width = '80px';
          captchaButton.style.height = '32px';
          
          captchaInput.style.paddingRight = '90px';
          
          // 展示图形验证码按钮
          setTimeout(() => {
            captchaButton.style.visibility = 'visible';
          }, 50);
        }
      }
    }, 0);
  });
}

// 组件挂载后定位按钮
onMounted(() => {
  setTimeout(() => {
    positionSmsButton();
    positionCaptchaButton();
  }, 500);
  window.addEventListener('resize', () => {
    positionSmsButton();
    positionCaptchaButton();
  });
  // 自动获取图形验证码
  getCaptcha();

});
// 显示注册成功弹窗
function showRegistrationSuccessDialog(richTextContent: string) {
  console.log('传入的富文本内容:', richTextContent);
    // richTextContent = `<p style="text-align: start;"><strong>关于2024年度公司战略规划会议的通知</strong></p><p style="text-align: start;"><strong>各部门负责人及全体员工：</strong></p><p style="text-align: start;">为全面部署2024年度工作计划，深入分析市场形势，制定公司未来发展战略，公司定于<strong>2024年1月20日</strong>举办年度战略规划会议。现将相关事项通知如下：</p><h3 style="text-align: start; line-height: 1.5;"><strong>一、会议时间</strong></h3><p style="text-align: start;"><strong>2024年1月20日（周六）9:00-17:00</strong></p><h3 style="text-align: start; line-height: 1.5;"><strong>二、会议地点</strong></h3><p style="text-align: start;">上海浦东香格里拉大酒店3楼宴会厅</p><h3 style="text-align: start; line-height: 1.5;"><strong>三、参会人员</strong></h3><ol><li style="text-align: start;">公司高层管理团队</li><li style="text-align: start;">各部门负责人</li><li style="text-align: start;">各区域负责人</li><li style="text-align: start;">核心业务骨干</li></ol><h3 style="text-align: start; line-height: 1.5;"><strong>四、会议议程</strong></h3><ul><li style="text-align: start;">09:00-09:30 签到</li><li style="text-align: start;">09:30-10:30 董事长致辞及2023年度工作总结</li><li style="text-align: start;">10:30-12:00 各部门2023年度工作汇报</li><li style="text-align: start;">12:00-13:30 午餐交流</li><li style="text-align: start;">13:30-15:30 2024年度战略规划讨论</li></ul><p style="text-align: start;"><strong>注意事项：</strong></p><ol><li style="text-align: start;">请参会人员提前10分钟到场签到。</li><li style="text-align: start;">会议期间请保持手机静音。</li><li style="text-align: start;">需携带笔记本电脑或记事本用于记录。</li></ol><p style="text-align: start;"><strong>特此通知。</strong></p><p style="text-align: start;"><strong>公司行政部</strong><br>2024年1月15日</p>`;


  const dialogInstance = DialogPlugin({
    header: '',
    body: () => {
      return h('div', {
        class: 'success-dialog-container',
        style: {
          padding: '0',
          background: '#ffffff',
          overflow: 'hidden',
          position: 'relative'
        }
      }, [
        // 装饰元素 - 右上角圆形图标
        h('div', {
          style: {
            position: 'absolute',
            top: '20px',
            right: '20px',
            width: '60px',
            height: '60px',
            background: '#0052cc',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '24px',
            color: 'white'
          },
          innerHTML: '🧪'
        }),
        
        // 头部区域
        h('div', {
          style: {
            background: '#004099',
            padding: '40px 30px 30px',
            textAlign: 'center',
            color: 'white',
            position: 'relative',
            overflow: 'hidden'
          }
        }, [
          // 化工背景元素 - 分子结构
          h('div', {
            style: {
              position: 'absolute',
              top: '10px',
              left: '20px',
              fontSize: '48px',
              opacity: '0.18',
              transform: 'rotate(-15deg)',
              animation: 'float 6s ease-in-out infinite'
            }
          }, '⚛️'),
          
          // 化工背景元素 - 试管
          h('div', {
            style: {
              position: 'absolute',
              top: '20px',
              right: '30px',
              fontSize: '44px',
              opacity: '0.18',
              transform: 'rotate(10deg)',
              animation: 'float 8s ease-in-out infinite 1s'
            }
          }, '🧪'),
          
          // 化工背景元素 - 原子
          h('div', {
            style: {
              position: 'absolute',
              bottom: '20px',
              left: '20px',
              fontSize: '42px',
              opacity: '0.18',
              transform: 'rotate(5deg)',
              animation: 'float 7s ease-in-out infinite 2s'
            }
          }, '⚛️'),
          
          // 化工背景元素 - 化学符号
          h('div', {
            style: {
              position: 'absolute',
              bottom: '10px',
              right: '20px',
              fontSize: '40px',
              opacity: '0.18',
              transform: 'rotate(-5deg)',
              animation: 'float 9s ease-in-out infinite 0.5s'
            }
          }, '🔬'),
          
          // 新增化学元素 - 烧杯
          h('div', {
            style: {
              position: 'absolute',
              top: '60px',
              left: '10px',
              fontSize: '38px',
              opacity: '0.18',
              transform: 'rotate(-8deg)',
              animation: 'float 7.5s ease-in-out infinite 0.8s'
            }
          }, '🧪'),
          
          // 新增化学元素 - 分子球
          h('div', {
            style: {
              position: 'absolute',
              top: '50px',
              right: '10px',
              fontSize: '36px',
              opacity: '0.18',
              transform: 'rotate(12deg)',
              animation: 'float 8.5s ease-in-out infinite 1.2s'
            }
          }, '⚛️'),
          
          // 新增化学元素 - 化学公式
          h('div', {
            style: {
              position: 'absolute',
              bottom: '60px',
              left: '10px',
              fontSize: '34px',
              opacity: '0.18',
              transform: 'rotate(-3deg)',
              animation: 'float 6.5s ease-in-out infinite 1.5s'
            }
          }, '🔬'),
          
          // 新增化学元素 - 原子核
          h('div', {
            style: {
              position: 'absolute',
              bottom: '50px',
              right: '10px',
              fontSize: '32px',
              opacity: '0.18',
              transform: 'rotate(8deg)',
              animation: 'float 9.5s ease-in-out infinite 0.3s'
            }
          }, '⚛️'),
          
          // 化工背景元素 - 分子连接线
          h('div', {
            style: {
              position: 'absolute',
              top: '50%',
              left: '8%',
              width: '80px',
              height: '3px',
              background: 'rgba(255, 255, 255, 0.15)',
              transform: 'rotate(45deg)',
              animation: 'pulse 4s ease-in-out infinite',
              borderRadius: '2px'
            }
          }),
          
          h('div', {
            style: {
              position: 'absolute',
              top: '25%',
              right: '12%',
              width: '60px',
              height: '3px',
              background: 'rgba(255, 255, 255, 0.15)',
              transform: 'rotate(-30deg)',
              animation: 'pulse 4s ease-in-out infinite 1s',
              borderRadius: '2px'
            }
          }),
          
          // 添加更多分子连接线
          h('div', {
            style: {
              position: 'absolute',
              top: '70%',
              left: '15%',
              width: '60px',
              height: '4px',
              background: 'rgba(255, 255, 255, 0.18)',
              transform: 'rotate(20deg)',
              animation: 'pulse 4s ease-in-out infinite 2s',
              borderRadius: '2px'
            }
          }),
          
          h('div', {
            style: {
              position: 'absolute',
              top: '15%',
              right: '25%',
              width: '55px',
              height: '4px',
              background: 'rgba(255, 255, 255, 0.18)',
              transform: 'rotate(-15deg)',
              animation: 'pulse 4s ease-in-out infinite 1.5s',
              borderRadius: '2px'
            }
          }),
          
          // 新增更多分子连接线
          h('div', {
            style: {
              position: 'absolute',
              top: '40%',
              left: '5%',
              width: '70px',
              height: '4px',
              background: 'rgba(255, 255, 255, 0.18)',
              transform: 'rotate(35deg)',
              animation: 'pulse 4s ease-in-out infinite 0.8s',
              borderRadius: '2px'
            }
          }),
          
          h('div', {
            style: {
              position: 'absolute',
              top: '80%',
              right: '8%',
              width: '65px',
              height: '4px',
              background: 'rgba(255, 255, 255, 0.18)',
              transform: 'rotate(-25deg)',
              animation: 'pulse 4s ease-in-out infinite 2.5s',
              borderRadius: '2px'
            }
          }),
          
          h('div', {
            style: {
              position: 'absolute',
              top: '20%',
              left: '35%',
              width: '45px',
              height: '4px',
              background: 'rgba(255, 255, 255, 0.18)',
              transform: 'rotate(10deg)',
              animation: 'pulse 4s ease-in-out infinite 1.8s',
              borderRadius: '2px'
            }
          }),
          
          h('div', {
            style: {
              position: 'absolute',
              top: '60%',
              right: '35%',
              width: '50px',
              height: '4px',
              background: 'rgba(255, 255, 255, 0.18)',
              transform: 'rotate(-8deg)',
              animation: 'pulse 4s ease-in-out infinite 0.5s',
              borderRadius: '2px'
            }
          }),
          
          // 标题
          h('h1', {
            style: {
              fontSize: '28px',
              fontWeight: '700',
              margin: '0 0 12px 0',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '8px',
              position: 'relative',
              zIndex: '1'
            }
            // }, [h('span', '🎉'), h('span', '注册成功')]),
            }, [h('span', '注册成功')]),
          
          // 副标题
          h('p', {
            style: {
              fontSize: '16px',
              margin: '0 0 20px 0',
              fontWeight: '400',
              opacity: '0.9',
              position: 'relative',
              zIndex: '1'
            }
          }, '✨ 欢迎加入石化化工全链条大数据中心'),
          
          // 时间戳
          h('div', {
            style: {
              display: 'inline-flex',
              alignItems: 'center',
              gap: '6px',
              background: 'rgba(255, 255, 255, 0.2)',
              padding: '8px 16px',
              borderRadius: '20px',
              fontSize: '14px',
              fontFamily: 'monospace',
              position: 'relative',
              zIndex: '1'
            }
          }, [h('span', '📅'), h('span', `注册时间：${new Date().toLocaleString('zh-CN')}`)])
        ]),
        
        // 内容区域
        h('div', {
          style: {
            marginTop: '20px',
            padding: '0px',
            background: '#ffffff'
          }
        }, [
          h('div', {
            innerHTML: richTextContent,
            style: {
              maxHeight: '300px',
              overflowY: 'auto',
              lineHeight: '1.6',
              fontSize: '14px',
              color: '#374151'
            },
            class: 'rich-content-scroll'
          })
        ])
      ]);
    },
    confirmBtn: {
      content: '✅ 确认已阅读',
      variant: 'base',
      theme: 'primary'
    },
    cancelBtn: null,
    closeBtn: false,
    width: '750px',
    className: 'success-registration-dialog',
    showOverlay: true,
    closeOnOverlayClick: false,
    onConfirm: () => {
      dialogInstance.destroy();
    },
    onClose: () => {
      dialogInstance.destroy();
    }
  });
}

// 注册
async function handleSubmit(value: Recordable<any>) {
  try {
    // 验证图形验证码
    if (!value.captcha) {
      MessagePlugin.error('请输入图形验证码');
      return;
    }

    loading.value = true;
    
    const submitData = {
      ...value,
      captcha: value.captcha,
      checkKey: captchaKey.value
    };
    
    await registerApi(submitData);
    MessagePlugin.success("注册成功，即将跳转登录页面");

    // 获取通知内容并显示弹窗
    try {
      const notificationData = await getNotification('register');
      if (notificationData && notificationData.length > 0) {
        // 显示注册成功弹窗
        showRegistrationSuccessDialog(notificationData[0].content);
      }
    } catch (error) {
      console.error('获取通知内容失败:', error);
    }
    
    setTimeout(() => {
      router.push({
        path: '/auth/login',
        query: {
          username: value.accountNumber
        }
      });
    }, 100);
  } catch (error) {
    console.error('注册失败:', error);
    if (error.code !== '9901'){
      MessagePlugin.error('注册失败，请重试');
    }
  } finally {
    loading.value = false;
  }
}

const formSchema = computed((): VbenFormSchema[] => {
  return [
    {
      component: 'VbenInput',
      componentProps: {
        // placeholder: $t('authentication.usernameTip'),
        placeholder: '请输入账号',
        autocomplete: 'new-username',
      },
      fieldName: 'accountNumber',
      label: $t('authentication.username'),
      rules: z.string()
        .min(1, { message: $t('authentication.usernameTip') })
        .max(50, { message: '账号最大长度50位' }),
    },
    {
      component: 'VbenInputPassword',
      componentProps: {
        passwordStrength: true,
        placeholder: $t('authentication.password'),
        autocomplete: 'new-password',
      },
      fieldName: 'password',
      label: $t('authentication.password'),
      renderComponentContent() {
        return {
          strengthText: () => $t('authentication.passwordStrength'),
        };
      },
      rules: z.string()
        .min(8, { message: '密码至少8位' })
        .regex(/^(?=.*[a-zA-Z])(?=.*\d).+$/, { message: '密码必须包含数字和英文' }),
    },
    {
      component: 'VbenInputPassword',
      componentProps: {
        placeholder: $t('authentication.confirmPassword'),
        autocomplete: 'new-password',
      },
      dependencies: {
        rules(values) {
          const { password } = values;
          return z
            .string({ required_error: '请输入确认密码' })
            .min(8, { message: '密码至少8位' })
            .regex(/^(?=.*[a-zA-Z])(?=.*\d).+$/, { message: '密码必须包含数字和英文' })
            .refine((value) => value === password, {
              message: '两次输入的密码不一致',
            });
        },
        triggerFields: ['password'],
      },
      fieldName: 'confirmPassword',
      label: $t('authentication.confirmPassword'),
    },
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: '请输入姓名',
        autocomplete: 'name',
      },
      fieldName: 'realName',
      label: '姓名',
      rules: z.string()
        .min(1, { message: '请输入姓名' })
        .max(50, { message: '姓名最大长度50位' }),
    },
              {
      component: 'VbenInput',
      componentProps: {
        placeholder: '请输入手机号',
        autocomplete: 'tel',
      },
      fieldName: 'phoneNumber',
      label: '手机号',
      rules: z.string()
        .min(1, { message: '请输入手机号' })
        .regex(/^1[3-9]\d{9}$/, { message: '请输入正确的手机号格式' }),
              },
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: '请输入图形验证码',
        autocomplete: 'off',
      },
      fieldName: 'captcha',
      label: '图形验证码',
      rules: z.string()
        .min(1, { message: '请输入图形验证码' }),
    },
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: '请输入验证码',
        autocomplete: 'off',
      },
      fieldName: 'smsCode',
      label: '短信验证码',
      rules: z.string().min(1, { message: '请输入短信验证码' }),
    },
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: '请输入邮箱',
        autocomplete: 'email',
      },
      fieldName: 'email',
      label: '邮箱',
      rules: z.string()
        .min(1, { message: '请输入邮箱' })
        .max(50, { message: '邮箱最大长度50位' })
        .email({ message: '请输入正确的邮箱格式' }),
    },
    // {
    //   component: 'VbenCheckbox',
    //   fieldName: 'agreePolicy',
    //   renderComponentContent: () => ({
    //     default: () =>
    //       h('span', [
    //         $t('authentication.agree'),
    //         h(
    //           'a',
    //           {
    //             class: 'vben-link ml-1',
    //             href: '',
    //           },
    //           `${$t('authentication.privacyPolicy')} & ${$t('authentication.terms')}`,
    //         ),
    //       ]),
    //   }),
    //   rules: z.boolean().refine((value) => !!value, {
    //     message: $t('authentication.agreeTip'),
    //   }),
    // },
  ];
});

// async function handleSubmit(value: Recordable<any>) {
//   try {
//     loading.value = true;
//   await registerApi(value);
//     MessagePlugin.success("注册成功，即将跳转登录页面");
    
//     // 注册成功后，跳转到登录页面，并带上注册的账号
//     setTimeout(() => {
//       router.push({
//         path: '/auth/login',
//         query: {
//           username: value.accountNumber
//         }
//   });
//     }, 100);
//   } catch (error) {
//     console.error('注册失败:', error);
//   } finally {
//     loading.value = false;
//   }
// }
</script>

<template>
  <div class="register-wrapper">
    <AuthenticationRegister
      ref="registerFormRef"
      :form-schema="formSchema"
      :loading="loading"
      @submit="handleSubmit"
    />
    
    <!-- 验证码发送按钮 -->
    <div 
      ref="smsButtonRef"
      class="sms-code-button"
      :class="[
        'px-3 py-1 text-sm rounded border transition-colors duration-200 whitespace-nowrap',
        smsCodeCountdown > 0 || smsCodeSending
          ? 'bg-gray-100 text-gray-400 cursor-not-allowed border-gray-200'
          : 'sendSmsCode-button-500 text-white hover:bg-blue-600 border-blue-500 cursor-pointer'
      ]"
      @click="sendSmsCode"
    >
      {{ smsCodeSending ? '发送中...' : smsCodeText }}
    </div>
    
    <!-- 图形验证码图片按钮 -->
    <div 
      ref="captchaButtonRef"
      class="captcha-button"
      :class="[
        'text-sm rounded border transition-colors duration-200 whitespace-nowrap',
        captchaLoading
          ? 'bg-gray-100 text-gray-400 cursor-not-allowed border-gray-200'
          : 'border-gray-300 cursor-pointer hover:border-gray-400'
      ]"
      @click="getCaptcha"
    >
      <img
        v-if="captchaImage && !captchaLoading"
        :src="captchaImage"
        alt="验证码"
        class="captcha-image-button"
        title="点击刷新验证码"
      />
      <div v-else-if="captchaLoading" class="captcha-loading-button">
        <div class="spinner"></div>
      </div>
      <div v-else class="captcha-error-button">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
      </div>
    </div>
  </div>
</template>

<style scoped>
.register-wrapper {
  position: relative;
}

.sms-code-button {
  position: absolute;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto;
  visibility: hidden;
}

.register-wrapper :deep(input[placeholder*="验证码"]:not([placeholder*="图形验证码"])) {
  padding-right: 110px !important;
}

.register-wrapper :deep(input[placeholder*="图形验证码"]) {
  padding-right: 90px !important;
}

.sendSmsCode-button-500 {
  background-color: hsl(var(--primary));
}

/* 图形验证码按钮样式 */
.captcha-button {
  position: absolute;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto;
  visibility: hidden;
  width: 97px !important;
}

.captcha-image-button {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.captcha-loading-button {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.captcha-error-button {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 化工元素动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(var(--rotation, 0deg));
  }
  50% {
    transform: translateY(-10px) rotate(var(--rotation, 0deg));
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.18;
    transform: scale(1) rotate(var(--rotation, 0deg));
  }
  50% {
    opacity: 0.5;
    transform: scale(1.3) rotate(var(--rotation, 0deg));
  }
}

/* 注册成功弹窗样式 */
:deep(.success-registration-dialog) {
  .t-dialog__ctx .t-dialog__position.t-dialog--top {
    align-items: center !important;
    padding-top: unset !important;
  }
}

/* 富文本内容样式优化 */
.rich-content-scroll h3 {
  color: #1e293b;
  font-size: 18px;
  font-weight: 700;
  margin: 24px 0 12px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e2e8f0;
}

.rich-content-scroll p {
  margin: 12px 0;
  color: #475569;
  line-height: 1.7;
  font-size: 15px;
}

.rich-content-scroll strong {
  color: #1e293b;
  font-weight: 700;
}

.rich-content-scroll ol, .rich-content-scroll ul {
  margin: 16px 0;
  padding-left: 24px;
}

.rich-content-scroll li {
  margin: 8px 0;
  color: #475569;
  line-height: 1.6;
}

.rich-content-scroll ol li {
  list-style-type: decimal;
}

.rich-content-scroll ul li {
  list-style-type: disc;
}
</style>
