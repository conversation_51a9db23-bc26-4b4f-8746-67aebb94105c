// src/api/silicon-data-model.ts
import { requestClient } from '#/api/request';

export async function getStatistics(data) {
  return requestClient.get<any[]>(
    '/rgdc-statistics/tDataBase/statistics',
    data,
  );
}

export async function getIndustryCharacteristicsData() {
  return requestClient.post<any[]>(
    '/rgdc-submit/tDataBase/getIndustryCharacteristicsData',
  );
}

// 新版本首页接口
export async function getHomeDisplayList() {
  return requestClient.get<any[]>(
    '/rgdc-submit/tDataClassify/getHomeDisplayList',
  );
}
export async function getResourcesTypeWithCount(_data?: any) {
  return requestClient.post<any[]>(
    '/rgdc-search/tSearch/getResourcesTypeWithCount',
  );
}
// 获取首页展示的数据服务信息
export async function getServiceHome() {
  return requestClient.get<any[]>('/rgdc-sys/tDataService/getServiceHome');
}
