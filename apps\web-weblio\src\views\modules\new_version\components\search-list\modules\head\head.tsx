import type { PropType } from 'vue';

import { ViewListIcon, ViewModuleIcon } from 'tdesign-icons-vue-next';
import { Select } from 'tdesign-vue-next';
import { defineComponent } from 'vue';

import styles from './head.module.less';

export default defineComponent({
  name: 'SearchListHead',
  props: {
    total: {
      type: Number,
      required: true,
    },
    sortType: {
      type: String as PropType<'count' | 'date' | 'default'>,
      required: true,
    },
    sortOptions: {
      type: Array as PropType<{ label: string; value: string }[]>,
      required: true,
    },
    viewType: {
      type: String as PropType<'card' | 'list'>,
      required: true,
    },
  },
  emits: ['update:sortType', 'update:viewType', 'sortChange'],
  setup(props, { emit }) {
    const handleSortChange = (value: 'count' | 'date' | 'default') => {
      emit('update:sortType', value);
      emit('sortChange');
    };

    const handleViewChange = (view: 'card' | 'list') => {
      emit('update:viewType', view);
    };

    return () => (
      <div class={styles['list-header']}>
        <div class={styles['result-count']}>
          共 <span class={styles.highlight}>{props.total}</span> 条结果
        </div>
        <div class={styles['list-actions']}>
          <Select
            modelValue={props.sortType}
            onChange={handleSortChange}
            options={props.sortOptions}
            placeholder="排序方式"
          />
          <div class={styles['view-switch']}>
            <div
              class={[
                styles['switch-btn'],
                props.viewType === 'card' ? styles.active : '',
              ]}
              onClick={() => handleViewChange('card')}
            >
              <ViewModuleIcon />
            </div>
            <div
              class={[
                styles['switch-btn'],
                props.viewType === 'list' ? styles.active : '',
              ]}
              onClick={() => handleViewChange('list')}
            >
              <ViewListIcon />
            </div>
          </div>
        </div>
      </div>
    );
  },
});
