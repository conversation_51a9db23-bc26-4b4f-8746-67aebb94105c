import { requestClient } from '#/api/request';

// 工厂数据API接口类型定义
export interface LoginParams {
  userId: string;
  pwd: string;
}

export interface LoginResponse {
  code: string;
  data: string; // token
  msg: string;
}

export interface QueryDataParams {
  type: string;
  operate: number; // 1-实时数据 2-漏更数据 3-历史数据
  startDate?: string; // operate=3时必填，格式：yyyy-MM-dd
  endDate?: string; // operate=3时必填，格式：yyyy-MM-dd
}

export interface FactoryDataItem {
  Price: string;
  IDcode: string;
  Code: string;
  Date: string;
  Name: string;
  Unit?: string;
  categoryOne?: string; // 一级分类
  categoryTwo?: string; // 二级分类
}

export interface QueryDataResponse {
  code: string;
  msg: string;
  data: FactoryDataItem[];
}
// 查询工厂数据
export async function queryFactoryData() {
  return requestClient.get('/rgdc-submit/factoryData/getJlcData');
}

// 查询工厂数据类型
export async function getCategoryInfo() {
  return requestClient.get('/rgdc-submit/factoryData/getCategoryInfo');
}
