.imageCover {
  width: 100%;
  height: 100%;
  max-width: 250px;
  margin: 20px;
  object-fit: cover;

}

.container {
  width: 100%;
  height: 100%;
  max-width: 250px;
  margin:  20px;
  background-color: #f5eeda; /* Fallback color */
  background-image: radial-gradient(
      ellipse at 20% 80%,
      rgba(189, 174, 148, 0.35),
      transparent 40%
    ),
    radial-gradient(ellipse at 75% 25%, rgba(196, 182, 155, 0.3), transparent 35%),
    radial-gradient(ellipse at 50% 50%, rgba(204, 192, 168, 0.25), transparent 60%),
    linear-gradient(
      135deg,
      rgba(245, 238, 218, 1) 0%,
      rgba(239, 227, 200, 1) 100%
    );
  border: 1px solid #d1c7ac;
  box-shadow: 0 0 0 4px #eadeb8, 0 0 0 5px #d1c7ac;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20px;
  box-sizing: border-box;
  font-family: '<PERSON>T<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', serif;
  color: #5a4d33;
  text-align: center;
}

.header,
.footer {
  flex-shrink: 0;
  p {
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
  }
}

.header {
  border-bottom: 1px solid #d1c7ac;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.main {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #d1c7ac;
  padding: 20px 0;
}

.title {
  font-size: 32px;
  font-weight: bold;
  margin: 0 0 10px;
  letter-spacing: 4px;
}

.author {
  font-size: 18px;
  margin: 0;
}

.decoration {
  margin: 20px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80%;

  .line {
    flex-grow: 1;
    height: 1px;
    background-color: #d1c7ac;
  }

  .flourish {
    font-size: 24px;
    margin: 0 15px;
    color: #8c7b5e;
  }
}

.footer {
  padding-top: 10px;
  margin-top: 20px;
}
