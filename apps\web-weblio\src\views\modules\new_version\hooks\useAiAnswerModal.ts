import { ref } from 'vue';

// 1. 在 hook 外部创建单例状态，确保全局唯一
const isVisible = ref(false);
const onSuccessCallback = ref<((result: any) => void) | null>(null);

/**
 * @description AI问答弹窗 hook
 */
export function useAiAnswerModal() {
  // 2. 定义打开弹窗的函数
  const openModal = (onSuccess?: (result: any) => void) => {
    onSuccessCallback.value = onSuccess || null;
    isVisible.value = true;
  };

  // 3. 定义关闭弹窗的函数
  const closeModal = () => {
    isVisible.value = false;
    onSuccessCallback.value = null;
  };

  // 4. 暴露状态和方法
  return {
    isVisible,
    onSuccessCallback,
    openModal,
    closeModal,
  };
}
