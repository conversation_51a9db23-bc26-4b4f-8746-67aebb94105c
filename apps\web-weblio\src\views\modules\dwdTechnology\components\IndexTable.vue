<script setup lang="ts">
import type { PageInfo } from 'tdesign-vue-next';

import { getDictItems } from '#/api';
import { Pagination, Space, Table, Button, Tag, Dialog, Form, FormItem, Input, Select, Textarea, MessagePlugin } from 'tdesign-vue-next';
import { onMounted, reactive, ref, computed } from 'vue';
import { useRequest } from 'vue-hooks-plus';

import type { Recordable } from '@vben/types';
import { listByPage, getFilterOptions, technologyDataApply } from '../api';
import { useAuthStore } from '#/store';

const authStore = useAuthStore();

/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * reload触发时二次传入数据
   */
  tagObj: {},
  /**
   * 被选中key
   */
  selectedRowKeys: [],
  /**
   * 删除提示显示标志
   */
  delDailogShow: false,
  /**
   * 数据元
   */
  dataSource: [],
  /**
   * 查询展开/关闭多字段
   */
  hideQuery: false,
  /**
   * 加载状态
   */
  loading: false,
  /**
   * table排序
   */
  sort: [],
});

// 定义表格行的类型（可根据实际字段补充）
interface TableRowType {
  id: string | number;
  status?: string;
  [key: string]: any; // 允许其他动态字段
}

/**
 * 分页参数
 */
const Paginations = {
  current: 1,
  pageSize: 20,
  total: 0,
};
const pagination: any = ref(Paginations);
const searchKeyword = ref('');
// 职业
const careerOptions = ref([]);
// 筛选状态
const techFieldFilter = ref('');
const techLinkFilter = ref('');
const carbonReductionFilter = ref('');
const techMaturityFilter = ref('');

// 筛选选项数据
const filterOptions = ref({
  techField: [],
  techLink: [],
  carbonReduction: [],
  techMaturity: [],
});

// 申请对话框相关状态
const applyDialogVisible = ref(false);
const applyForm = ref({
  applicantName: '',
  corporateName: '',
  applicantEmail: '',
  career: '',
  phoneNumber: '',
  verificationCode: '',
  mobileVerifyCode: '',
  remark: ''
});
const currentApplyRow = ref<TableRowType | null>(null);

// 验证码相关
const smsCodeCountdown = ref(0);
const smsCodeText = ref('发送验证码');
const smsCodeValue = ref('');

// 验证码相关状态
const mobileCaptchaImage = ref('');
const mobileCaptchaKey = ref('');
const mobileCaptchaLoading = ref(false);
const mobileCaptchaError = ref('');
const showMobileCaptchaError = ref(false);

const verifyCodeError = ref('');
const showVerifyCodeError = ref(false);
const verifyCodeTouched = ref(false);

// 验证码相关响应式数据
const captchaImage = ref('');
const captchaKey = ref('');
const captchaLoading = ref(false);

const smsCountdown = ref(0);
const smsLoading = ref(false);
const smsText = computed(() => {
  return smsCountdown.value > 0 ? `${smsCountdown.value}s后重新发送` : '获取验证码';
});

// UUID生成所需的16进制字符
const hexList = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'];

/**
 * 网络访问方法定义
 */
const reqRunner = {
  /**
   * 分页获得列表数据
   */
  listByPage: useRequest(listByPage, {
    // 不自动请求
    manual: true,
    // 300毫秒防抖
    debounceWait: 300,
    onError: () => { },
    onSuccess: (res: any) => {
      const { records, total } = res;
      state.dataSource = records;
      pagination.value = {
        ...pagination.value,
        total,
      };

      if (records.length === 0 && total !== 0) {
        pagination.value.current = 1;
        reload();
      }
    },
  }),
};

/**
 * 重新加载数据
 * @param data
 */
function reload(data?: any) {
  if (data) {
    state.tagObj = data;
  }

  const { run, loading } = reqRunner.listByPage;
  state.loading = loading;

  const params = {
    param: {
      techName: searchKeyword.value,
      techField: techFieldFilter.value, // 添加领域筛选参数
      techLink: techLinkFilter.value, // 添加环节筛选参数
      carbonReduction: carbonReductionFilter.value, // 添加减碳贡献等级筛选参数
      techMaturity: techMaturityFilter.value // 添加技术成熟度筛选参数
    },
    current: pagination.value.current,
    pageSize: pagination.value.pageSize,
  };

  console.log('API 调用参数:', params);

  run(params);
}

/**
 * 查询Form提交，执行查询
 */
const handleSearch = () => {
  // 重置分页
  pagination.value = {
    current: 1,
    pageSize: pagination.value.pageSize,
  };
  setTimeout(() => {
    reload();
  }, 0);
};

/**
 * table分页变化时调用
 * @param pageInfo
 */
const rehandlePageChange = (pageInfo: PageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  reload();
};

// 获取技术成熟度等级的颜色和样式
const getMaturityStyle = (maturity: string) => {
  if (!maturity) {
    return { backgroundColor: '#fff', color: '#fff', text: '--' }; // 统一返回默认样式对象
  }
  // 截取最后一位数字
  const lastChar = maturity.slice(-1);
  const level = parseInt(lastChar);

  if (level <= 3) {
    return { backgroundColor: '#ff4d4f', color: '#fff', text: maturity };
  } else if (level > 3 && level <= 6) {
    return { backgroundColor: '#fa8c16', color: '#fff', text: maturity };
  } else if (level > 6) {
    return { backgroundColor: '#52c41a', color: '#fff', text: maturity };
  }
};

// 获取减碳贡献等级的颜色
const getCarbonReductionStyle = (level: string) => {
  if (!level) {
    return { backgroundColor: '#fff', color: '#fff' };
  }
  if (level === '低') {
    return { backgroundColor: '#ff4d4f', color: '#fff' };
  } else if (level === '中') {
    return { backgroundColor: '#fa8c16', color: '#fff' };
  } else if (level === '高') {
    return { backgroundColor: '#52c41a', color: '#fff' };
  } else {
    return { backgroundColor: '#1890ff', color: '#fff' };
  }
};

// 技术领域筛选选项
const techFieldOptions = computed(() => {
  return [{ label: '全部', value: '' }, ...filterOptions.value.techField];
});

// 环节筛选选项
const techLinkOptions = computed(() => {
  return [{ label: '全部', value: '' }, ...filterOptions.value.techLink];
});

// 减碳贡献等级筛选选项
const carbonReductionOptions = computed(() => {
  return [{ label: '全部', value: '' }, ...filterOptions.value.carbonReduction];
});

// 技术成熟度筛选选项 (固定1-9)
const techMaturityOptions = computed(() => {
  return [{ label: '全部', value: '' }, ...filterOptions.value.techMaturity];
});

// 表格列定义
const columns = ref([
  {
    colKey: 'techMaturity',
    title: '技术成熟度',
    width: 100,
    // 单选过滤配置
    filter: {
      type: 'single' as const,
      list: techMaturityOptions,
      confirmEvents: ['onChange'],
    },
    cell: (h: any, { row }: any) => {
      if (!row.techMaturity) {
        return '';
      }
      const style = getMaturityStyle(row.techMaturity);
      if (!style) return '';
      return h(Tag, {
        style: {
          width: '30%',
          lineHeight: '1.5em',
          display: 'inline-table',
          textAlign: 'center',
          padding: '4px 8px',
          borderRadius: '4px',
          backgroundColor: style.backgroundColor,
          color: style.color,
          fontSize: '14px',
          fontWeight: 'bold'
        }
      }, () => style.text);
    }
  },
  {
    colKey: 'techField',
    title: '领域',
    width: 200,
    ellipsis: true,
    // 单选过滤配置
    filter: {
      type: 'single' as const,
      list: techFieldOptions,
      confirmEvents: ['onChange'],
    },
  },
  {
    colKey: 'techName',
    title: '技术',
    width: 200,
    ellipsis: true,
  },
  {
    colKey: 'techLink',
    title: '环节',
    width: 150,
    ellipsis: true,
    // 单选过滤配置
    filter: {
      type: 'single' as const,
      list: techLinkOptions,
      confirmEvents: ['onChange'],
    },
  },
  {
    colKey: 'carbonReduction',
    title: '减碳贡献等级',
    width: 80,
    // 单选过滤配置
    filter: {
      type: 'single' as const,
      list: carbonReductionOptions,
      confirmEvents: ['onChange'],
    },
    cell: (h: any, { row }: any) => {
      if (!row.carbonReduction) {
        return '';
      }
      const style = getCarbonReductionStyle(row.carbonReduction);
      if (!style) return '';
      return h(Tag, {
        style: {
          width: '30%',
          lineHeight: '1.8em',
          display: 'inline-table',
          textAlign: 'center',
          padding: '4px 8px',
          borderRadius: '4px',
          backgroundColor: style.backgroundColor,
          color: style.color,
          fontSize: '12px',
          fontWeight: 'bold'
        }
      }, () => row.carbonReduction);
    }
  },
  {
    colKey: 'operation',
    title: '审批状态&操作',
    width: 80,
    cell: (h: any, { row }: any) => {
      if (!row.status) {
        return h(Button, {
          theme: 'primary',
          variant: 'outline',
          onClick: () => handleApply(row)
        }, () => '申请');
      } else if (row.status === '2') {
        return h('div', { style: { display: 'flex', gap: '8px' } }, [
          h('span', {
            style: {
              color: 'red',
              lineHeight: '2.4em',
            }
          }, '驳回'),
          h(Button, {
            theme: 'primary',
            variant: 'outline',
            onClick: () => handleApply(row)
          }, '重新申请'),
        ]);
      } else {
        return h('span', {
          style: {
            color: row.status === '0' ? 'orange' : 'green'
          }
        }, row.status === '0' ? '已提交申请' : '申请成功');
      }
    }
  }
]);

// 处理申请按钮点击
const handleApply = (row: TableRowType) => {
  applyDialogVisible.value = true;
  // 重置表单
  applyForm.value = {
    applicantName: row.applicantName || '',
    corporateName: row.corporateName || '',
    applicantEmail: row.applicantEmail || '',
    career: row.career || '',
    phoneNumber: row.phoneNumber || '',
    verificationCode: '',
    mobileVerifyCode: '',
    remark: row.remark || ''
  };
};

// 处理申请提交
const handleApplySubmit = async () => {
  // 这里可以添加表单验证逻辑
  if (!applyForm.value.applicantName || !applyForm.value.corporateName
    || !applyForm.value.applicantEmail || !applyForm.value.career
    || !applyForm.value.phoneNumber || !applyForm.value.verificationCode
    || !applyForm.value.mobileVerifyCode) {
    MessagePlugin.warning('请填写完整的申请信息');
    return;
  }

  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  if (!emailRegex.test(applyForm.value.applicantEmail)) {
    MessagePlugin.error('请输入正确的邮箱格式');
    return;
  }

  // 验证手机号格式
  const phoneNumberRegex = /^1[3-9]\d{9}$/;
  if (!phoneNumberRegex.test(applyForm.value.phoneNumber)) {
    MessagePlugin.warning('请输入正确的手机号格式');
    return;
  }

  if (applyForm.value.verificationCode.length < 4) {
    MessagePlugin.error('图形验证码长度不正确');
    return;
  }

  if (applyForm.value.mobileVerifyCode.length < 4) {
    MessagePlugin.error('短信验证码长度不正确');
    return;
  }

  // 提交申请逻辑
  const param = {
    ...applyForm.value,
    operationCode: currentApplyRow.value ? currentApplyRow.value.techUniq : '',
    operationName: currentApplyRow.value ? currentApplyRow.value.techName : '',
    remark: applyForm.value.remark || '',
  }
  const res = await technologyDataApply(param);

  if (res && res === 'Success') {
    // 更新行状态
    if (currentApplyRow.value) {
      currentApplyRow.value.status = '0';// 更新状态为已提交申请
    }

    // 关闭对话框
    applyDialogVisible.value = false;
    MessagePlugin.success('申请提交成功，请等待审批');
  } else {
    // 显示错误信息
    MessagePlugin.error('申请提交失败');
  }
};

// 处理对话框取消
const handleApplyCancel = () => {
  applyDialogVisible.value = false;
};

// 处理筛选
const onFilterChange = (filters) => {
  console.log('筛选变更:', filters);

  // 更新筛选状态
  techFieldFilter.value = filters.techField || '';
  techLinkFilter.value = filters.techLink || '';
  carbonReductionFilter.value = filters.carbonReduction || '';
  techMaturityFilter.value = filters.techMaturity || '';

  // 重置分页到第一页
  pagination.value = {
    ...pagination.value,
    current: 1,
  };

  // 立即触发数据加载
  reload();
};

// 获取筛选选项
async function loadFilterOptions() {
  try {
    const response = await getFilterOptions();
    if (response) {
      // 处理领域选项
      if (response.techMaturity) {
        filterOptions.value.techMaturity = response.techMaturity.map(item => ({
          label: item,
          value: item
        }));
      }

      // 处理领域选项
      if (response.techField) {
        filterOptions.value.techField = response.techField.map(item => ({
          label: item,
          value: item
        }));
      }

      // 处理环节选项
      if (response.techLink) {
        filterOptions.value.techLink = response.techLink.map(item => ({
          label: item,
          value: item
        }));
      }

      // 处理减碳贡献等级选项，并按低、中、高排序
      if (response.carbonReduction) {
        filterOptions.value.carbonReduction = response.carbonReduction.map(item => ({
          label: item,
          value: item
        }));
      }
    }
  } catch (error) {
    console.error('获取筛选选项失败:', error);
  }
};

// 验证手机号登录图形验证码
function validateMobileCaptcha() {
  showMobileCaptchaError.value = false;
  mobileCaptchaError.value = '';

  if (!applyForm.value.verificationCode) {
    mobileCaptchaError.value = '请输入图形验证码';
    showMobileCaptchaError.value = true;
    return false;
  }

  if (applyForm.value.verificationCode.length < 4) {
    mobileCaptchaError.value = '图形验证码长度不正确';
    showMobileCaptchaError.value = true;
    return false;
  }

  return true;
}

// 清除验证码错误
function clearVerifyCodeError() {
  if (showVerifyCodeError.value) {
    showVerifyCodeError.value = false;
    verifyCodeError.value = '';
  }
}

// 处理验证码输入
function handleVerifyCodeInput() {
  verifyCodeTouched.value = true; // 标记用户已经开始输入
  clearVerifyCodeError();
}

// 验证码验证函数
function validateVerifyCode(force = false) {
  // 如果用户还没有开始输入过，且不是强制验证，则不显示错误
  if (!verifyCodeTouched.value && !force) {
    return true;
  }

  showVerifyCodeError.value = false;
  verifyCodeError.value = '';

  if (!applyForm.value.mobileVerifyCode) {
    verifyCodeError.value = '请输入短信验证码';
    showVerifyCodeError.value = true;
    return false;
  }

  if (applyForm.value.mobileVerifyCode.length < 4) {
    verifyCodeError.value = '短信验证码长度不正确';
    showVerifyCodeError.value = true;
    return false;
  }

  return true;
}

// 清除手机号登录验证码错误状态
function clearMobileCaptchaError() {
  if (showMobileCaptchaError.value) {
    showMobileCaptchaError.value = false;
    mobileCaptchaError.value = '';
  }
}

// 获取手机号登录图形验证码
async function getMobileCaptcha() {
  try {
    // 开始加载
    mobileCaptchaLoading.value = true;

    // 清空当前输入的验证码和图片
    applyForm.value.verificationCode = '';
    mobileCaptchaImage.value = '';

    // 清除验证码错误状态
    clearMobileCaptchaError();

    handleGetMobileCaptcha();

    // 如果5秒后还没有收到验证码，停止loading
    setTimeout(() => {
      if (mobileCaptchaLoading.value && !mobileCaptchaImage.value) {
        mobileCaptchaLoading.value = false;
      }
    }, 7000);
  } catch (error) {
    console.error('获取手机号登录图形验证码失败:', error);
    mobileCaptchaLoading.value = false;
  }
}

// 处理手机号登录获取图形验证码事件
async function handleGetMobileCaptcha() {
  try {
    const result = await getCaptchaImage();
    if (result) {
      mobileCaptchaImage.value = result.image;
      mobileCaptchaKey.value = result.key;
    }
    // 停止加载状态
    mobileCaptchaLoading.value = false;
  } catch (error) {
    console.error('处理手机号登录获取图形验证码事件失败:', error);
  }
}

// 获取图片验证码的方法
async function getCaptchaImage() {
  try {
    captchaLoading.value = true;

    // 生成验证码key
    captchaKey.value = `${new Date().getTime()}-${buildUUID()}`;

    // 调用后端API获取验证码图片
    const response = await authStore.getAuthCaptchaImage(captchaKey.value);

    // 如果返回的是base64图片数据
    captchaImage.value = response;

    return {
      image: captchaImage.value,
      key: captchaKey.value
    };
  } catch (error) {
    console.error('获取验证码失败:', error);


  } finally {
    captchaLoading.value = false;
  }
}

// 生成UUID的辅助函数
function buildUUID(): string {
  let uuid = '';
  for (let i = 1; i <= 36; i++) {
    if (i === 9 || i === 14 || i === 19 || i === 24) {
      uuid += '-';
    } else if (i === 15) {
      uuid += 4;
    } else if (i === 20) {
      uuid += hexList[(Math.random() * 4) | 8];
    } else {
      uuid += hexList[(Math.random() * 16) | 0];
    }
  }
  return uuid.replace(/-/g, '');
}

// 发送短信验证码
async function sendSmsCode() {

  try {
    const values = applyForm.value;

    if (!values.phoneNumber) {
      MessagePlugin.warning('请输入手机号');
      return;
    }

    // 验证手机号格式
    const phoneNumberRegex = /^1[3-9]\d{9}$/;
    if (!phoneNumberRegex.test(values.phoneNumber)) {
      MessagePlugin.warning('请输入正确的手机号格式');
      return;
    }

    if (!mobileCaptchaImage.value) {
      MessagePlugin.warning('请先获取图形验证码');
      return;
    }

    // 验证图形验证码
    if (!values.verificationCode) {
      MessagePlugin.error('请输入图形验证码');
      return;
    }

    if (values.verificationCode.length < 4) {
      MessagePlugin.error('图形验证码长度不正确');
      return;
    }

    smsLoading.value = true;

    // 调用发送验证码，传递图形验证码参数
    let res = await authStore.sendSmsCode(values.phoneNumber, values.verificationCode, captchaKey.value, 'login');
    if (res != null && res != undefined && res != '' && res['result'] == 'success') {
      MessagePlugin.success('验证码已发送，请注意查收');
      // 发送成功,开始倒计时
      startCountdown();
    } else if (res != null && res != undefined && res != '' && res['result'] == 'test') {
      MessagePlugin.success('验证码已发送，请注意查收');
      smsCodeValue.value = res['smsCode'];

      // 发送成功,开始倒计时
      startCountdown();
    } else {
      MessagePlugin.error('发送验证码失败，请重试');
    }

  } catch (error) {
    console.error('发送验证码失败:', error);
    if (error.code !== '9901') {
      MessagePlugin.error('发送验证码失败，请重试');
    }
    setTimeout(() => {
      // 清空当前输入的验证码和图片
      applyForm.value.verificationCode = '';
      mobileCaptchaImage.value = '';

      // 清除验证码错误状态
      clearMobileCaptchaError();

      handleGetMobileCaptcha();

    }, 1000);
  } finally {
    smsLoading.value = false;
  }
}

// 倒计时功能
function startCountdown() {
  smsCodeCountdown.value = 60;
  smsCodeText.value = `${smsCodeCountdown.value}秒后重发`;

  const timer = setInterval(() => {
    smsCodeCountdown.value--;
    if (smsCodeCountdown.value > 0) {
      smsCodeText.value = `${smsCodeCountdown.value}秒后重发`;
    } else {
      smsCodeText.value = '重新发送';
      clearInterval(timer);
    }
  }, 1000);
}

onMounted(async () => {
  careerOptions.value = await getDictItems('CAREER_TYPE');
  await loadFilterOptions();
  reload();
});

/**
 * 导出资源
 */
defineExpose({
  reload,
});
</script>

<template>
  <Space :size="16" class="w-full" direction="vertical">
    <!-- 查询表单定义区域 -->
    <div class="search-input-container">
      <!-- 搜索输入框 -->
      <input v-model="searchKeyword" type="text" class="search-input" placeholder="搜索工艺技术名称"
        @keyup.enter="handleSearch" />
      <!-- 搜索按钮 -->
      <button class="search-btn" @click="handleSearch">检索</button>
    </div>

    <!-- 表格区域 -->
    <div>
      <Table :data="state.dataSource" :columns="columns" :loading="state.loading" row-key="id" :bordered="true"
        resizable stripe hover table-layout="fixed" cell-empty-content="-" @filter-change="onFilterChange" />
      <br />
      <!-- 分页 -->
      <Pagination v-model:current="pagination.current" :total="pagination.total" :page-size="pagination.pageSize"
        @change="rehandlePageChange" class="mt-4" v-if="pagination.total > 0" />
    </div>

    <!-- 申请对话框 -->
    <Dialog v-model:visible="applyDialogVisible" header="工艺数据详情查看申请" width="50vw" :footer="false"
      @close="handleApplyCancel">
      <div class="apply-dialog-content">
        <div class="apply-dialog-subtitle">
          如需查看更多工艺数据详情，请填写以下信息，我们会尽快安排专业人员与您沟通。
        </div>

        <Form :data="applyForm" layout="vertical" class="apply-form">
          <div class="form-row">
            <FormItem name="applicantName" class="form-item-half">
              <template #label>
                <span class="required-label">*姓名</span>
              </template>
              <Input v-model="applyForm.applicantName" placeholder="请输入姓名" />
            </FormItem>
            <FormItem name="corporateName" class="form-item-half">
              <template #label>
                <span class="required-label">*机构名称</span>
              </template>
              <Input v-model="applyForm.corporateName" placeholder="请输入机构名称" />
            </FormItem>
          </div>

          <div class="form-row">
            <FormItem name="applicantEmail" class="form-item-half">
              <template #label>
                <span class="required-label">*邮箱</span>
              </template>
              <Input v-model="applyForm.applicantEmail" placeholder="请输入邮箱" />
            </FormItem>
            <FormItem name="career" class="form-item-half">
              <template #label>
                <span class="required-label">*职业</span>
              </template>
              <Select v-model="applyForm.career" :options="careerOptions" placeholder="请选择职业">
              </Select>
            </FormItem>
          </div>

          <div class="form-row">
            <FormItem name="phoneNumber" class="form-item-half">
              <template #label>
                <span class="required-label">*手机号</span>
              </template>
              <Input v-model="applyForm.phoneNumber" placeholder="请输入手机号" />
            </FormItem>
            <FormItem name="picCaptcha" class="form-item-half">
              <template #label>
                <span class="required-label">*图形验证码</span>
              </template>
              <div class="captcha-container">
                <div class="flex items-start space-x-2">
                  <div class="flex-1 captcha-input-wrapper">
                    <Input v-model="applyForm.verificationCode" type="text" placeholder="请输入图形验证码" :class="[
                      {
                        'border-red-500': showMobileCaptchaError
                      }
                    ]" maxlength="4" @input="clearMobileCaptchaError" @blur="() => validateMobileCaptcha()" />
                    <!-- 验证码错误提示 -->
                    <div class="captcha-error-area">
                      <div v-if="showMobileCaptchaError" class="captcha-error-message">
                        {{ mobileCaptchaError }}
                      </div>
                    </div>
                  </div>
                  <div class="flex items-start justify-center w-min-width captcha-image-wrapper">
                    <!-- 验证码图片 -->
                    <img v-if="mobileCaptchaImage && !mobileCaptchaLoading" :src="mobileCaptchaImage" alt="图形验证码"
                      class="captcha-image h-8 w-30 cursor-pointer border border-input rounded" title="点击刷新验证码"
                      @click="getMobileCaptcha" />

                    <!-- 验证码加载中 -->
                    <div v-if="mobileCaptchaLoading"
                      class="captcha-loading  h-8 w-30 border border-input rounded flex items-center justify-center cursor-pointer w-min-width"
                      title="点击刷新验证码" @click="getMobileCaptcha">
                      <div class="spinner"></div>
                    </div>

                    <!-- 验证码占位符 -->
                    <div v-else-if="!mobileCaptchaImage"
                      class="captcha-placeholder h-8 w-30 border border-input rounded flex items-center justify-center cursor-pointer w-min-width"
                      title="点击获取验证码" @click="getMobileCaptcha">
                      <span class="text-xs text-gray-400">点击获取</span>
                    </div>
                  </div>
                </div>
              </div>
            </FormItem>
          </div>

          <div class="form-row">
            <FormItem name="mobileCaptcha" class="form-item-half">
              <template #label>
                <span class="required-label">*短信验证码</span>
              </template>
              <!-- 短信验证码输入框和发送按钮 -->
              <div class="captcha-container">
                <div class="flex items-start space-x-2">
                  <div class="flex-1 captcha-input-wrapper">
                    <Input v-model="applyForm.mobileVerifyCode" type="text" placeholder="请输入短信验证码" :class="[
                      {
                        'border-red-500': showVerifyCodeError
                      }
                    ]" maxlength="6" @input="handleVerifyCodeInput" @blur="() => validateVerifyCode()" />
                    <!-- 验证码错误提示 -->
                    <div class="captcha-error-area">
                      <div v-if="showVerifyCodeError" class="captcha-error-message">
                        {{ verifyCodeError }}
                      </div>
                    </div>
                  </div>
                  <Button :disabled="smsCountdown > 0 || smsLoading" :class="[
                    'px-3 py-2 text-sm rounded border transition-colors duration-200 whitespace-nowrap w-min-width',
                    smsCountdown > 0 || smsLoading
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed border-gray-200'
                      : 'bg-blue-500 text-white commom-button-500 cursor-pointer'
                  ]" @click="sendSmsCode">
                    {{ smsLoading ? '发送中...' : smsText }}
                  </Button>
                </div>
              </div>
            </FormItem>
            <FormItem class="form-item-half"></FormItem>
          </div>

          <FormItem label="申请理由" name="remark">
            <Textarea v-model="applyForm.remark" placeholder="如有详细问题请填写，我们会仔细阅读并联系您给予回复。" :autosize="{ minRows: 5 }" />
          </FormItem>

          <div class="form-actions">
            <Button theme="primary" @click="handleApplySubmit" class="submit-btn">
              申请提交
            </Button>
          </div>
        </Form>
      </div>
    </Dialog>
  </Space>
</template>

<style lang="scss" scoped>
.search-input-container {
  position: relative;
  display: flex;
  width: 50%;
  margin-bottom: 20px;
}

.search-input {
  width: 100%;
  padding: 12px 20px;
  font-size: 14px;
  border: 1px solid #3182ce;
  border-radius: 24px;
  outline: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  &::placeholder {
    color: #9ca3af;
    font-size: 14px;
  }
}

.search-btn {
  position: absolute;
  top: 3px;
  right: 5px;
  padding: 10px 20px;
  border: none;
  border-radius: 24px;
  font-size: 14px;
  font-weight: 600;
  background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%);
  color: #fff;
  cursor: pointer;

  &:hover {
    background: linear-gradient(135deg, #2c5aa0 0%, #2a4d96 100%);
  }
}

/* 筛选区域样式 */
.filter-container {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 16px 20px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 16px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  min-width: 120px;

  &:focus {
    outline: none;
    border-color: #3182ce;
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
  }

  &:hover {
    border-color: #9ca3af;
  }
}

.filter-actions {
  margin-left: auto;
}

.reset-btn {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  color: #6b7280;
  cursor: pointer;

  &:hover {
    background: #f9fafb;
  }
}

/* 申请对话框样式 */
.apply-dialog-content {
  text-align: center;
  min-height: 55vh;
  max-height: 55vh;
}

.apply-dialog-subtitle {
  font-size: 16px;
  font-weight: bold;
  color: #666;
  margin-bottom: 24px;
  line-height: 1.5;
  padding: 10px 0;
}

.apply-form {
  .form-row {
    display: flex;
    gap: 16px;

    .form-item-half {
      flex: 1;
    }

    .form-item-phoneNumber {
      flex: 1;
    }

    .form-item-code {
      flex: 1;
    }
  }

  // 必填标志样式
  .required-label {
    color: #333;
    font-size: 14px;
    font-weight: 500;

    &::before {
      content: '';
      color: #e34d59;
      margin-right: 4px;
    }

    &::first-letter {
      color: #e34d59;
    }
  }

  .verification-container {
    display: flex;
    gap: 8px;
    align-items: center;

    .send-code-btn {
      white-space: nowrap;
      min-width: 100px;
    }
  }

  .form-actions {
    display: flex;
    justify-content: center;
    margin-top: 40px;

    .submit-btn {
      padding: 12px 24px;
      font-size: 16px;
      font-weight: 500;
    }
  }
}

.captcha-container {
  width: 100%;

  .captcha-input-wrapper {
    // 为输入框包装器设置固定结构
    display: flex;
    flex-direction: column;
  }

  .captcha-error-area {
    // 固定高度的错误提示区域，防止布局跳动
    height: 24px; // 预留错误提示的空间
    margin-top: 4px;
    display: flex;
    align-items: flex-start;
  }

  .captcha-error-message {
    font-size: 0.875rem; // text-sm
    color: #ef4444; // text-red-500
    line-height: 1.25rem;
    animation: fadeIn 0.2s ease-in-out;
  }

  .captcha-image-wrapper {
    // 验证码图片容器，确保与输入框顶部对齐
    padding-top: 0;
    min-height: 40px; // 与输入框高度保持一致
  }

  .captcha-image {
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 0.8;
    }
  }

  .refresh-btn {
    transition: color 0.2s ease;

    &:hover {
      color: hsl(var(--primary));
    }
  }

  .captcha-loading {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    transition: all 0.2s ease;

    &:hover {
      background: linear-gradient(135deg, #c3cfe2 0%, #f5f7fa 100%);
    }
  }

  .captcha-placeholder {
    width: 92px;
    padding-left: 10px;
    padding-right: 10px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: all 0.2s ease;

    &:hover {
      background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    }

    span {
      color: white !important;
    }
  }

  .spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0052d9;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  // 验证码输入框错误状态
  input.border-red-500 {
    border-color: #ef4444 !important;

    &:focus,
    &:focus-visible {
      outline: none;
      ring: 2px;
      ring-color: rgba(239, 68, 68, 0.2);
      border-color: #ef4444;
    }
  }
}

:deep(.t-table td),
:deep(.t-table th) {
  border-right: 1px solid #e6e8eb !important;
}

:deep(.t-table td:nth-child(1)),
:deep(.t-table th:nth-child(1)),
:deep(.t-table td:nth-child(5)),
:deep(.t-table th:nth-child(5)) {
  text-align: center;
}

:deep(.t-table td:last-child),
:deep(.t-table th:last-child) {
  border-right: none !important;
}

:deep(.t-table__filter-icon.t-is-focus),
:deep(.t-table__filter-icon:hover) {
  color: #fff;
}

:deep(.t-dialog__header .t-dialog__header-content) {
  font-size: 24px;
  justify-content: center;
}

:deep(.t-dialog) {
  overflow: hidden;
}

:deep(.t-dialog__body) {
  padding: 0;
}
</style>
