.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .result-count {
    color: #666;
    .highlight {
      color: #007bff;
      font-weight: bold;
    }
  }

  .list-actions {
    display: flex;
    align-items: center;
  }

  .view-switch {
    display: flex;
    margin-left: 16px;
    border: 1px solid #dcdcdc;
    border-radius: 4px;

    .switch-btn {
      padding: 4px 8px;
      cursor: pointer;
      background-color: #fff;
      color: #666;

      &.active {
        background-color: #007bff;
        color: #fff;
      }

      &:first-child {
        border-right: 1px solid #dcdcdc;
      }
    }
  }
}
