.container {
  padding: 60px;
  background-image: url('/static/home-page/imgs/search-bg-mask.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;

  @media screen and (max-width: 1024px) {
    padding: 40px;
  }

  @media screen and (max-width: 768px) {
    padding: 20px;
  }
}
.header {
  margin-bottom: 30px;

  @media screen and (max-width: 768px) {
    margin-bottom: 20px;
  }
}

.searchBox {
  display: flex;
  align-items: center;

  @media screen and (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }

  .searchButtons {
    flex-shrink: 0;
    margin-left: 30px;
    display: flex;
    align-items: flex-start;
    gap: 10px;
    flex-direction: column;
    justify-content: center;

    @media screen and (max-width: 768px) {
      margin-top: 15px;
      margin-left: 0;
      width: 100%;
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }

    .searchButton {
      font-weight: 500;
      font-size: 18px;
      color: #ffffff;
      cursor: pointer;

      @media screen and (max-width: 1024px) {
        font-size: 16px;
      }

      @media screen and (max-width: 480px) {
        font-size: 14px;
      }
    }
  }
  .arrow {
    margin-left: 5px;
  }
}
