import { defineStore } from 'pinia';
import { ref } from 'vue';

export interface TabOption {
  key: string;
  label: string;
  icon?: string;
  disabled?: boolean;
  children?: TabOption[];
}

export const useTabsStore = defineStore('tabs', () => {
  const activeTab = ref<string>('');
  const tabs = ref<TabOption[]>([]);

  const setActiveTab = (key: string) => {
    activeTab.value = key;
  };

  const setTabs = (options: TabOption[]) => {
    tabs.value = options;
    // 如果没有选中的tab且有选项，自动选中第一个
    if (!activeTab.value && options.length > 0 && options[0]) {
      activeTab.value = options[0].key;
    }
  };

  const addTab = (option: TabOption) => {
    const existingIndex = tabs.value.findIndex((tab) => tab.key === option.key);
    if (existingIndex === -1) {
      tabs.value.push(option);
    } else {
      tabs.value[existingIndex] = option;
    }
  };

  const removeTab = (key: string) => {
    const index = tabs.value.findIndex((tab) => tab.key === key);
    if (index !== -1) {
      tabs.value.splice(index, 1);
      // 如果删除的是当前选中的tab，自动选中第一个可用的tab
      if (activeTab.value === key && tabs.value.length > 0 && tabs.value[0]) {
        activeTab.value = tabs.value[0].key;
      }
    }
  };

  return {
    activeTab,
    tabs,
    setActiveTab,
    setTabs,
    addTab,
    removeTab,
  };
});
