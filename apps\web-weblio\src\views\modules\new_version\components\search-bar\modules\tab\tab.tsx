import type { PropType } from 'vue';

import { defineComponent } from 'vue';

import styles from './style.module.less';

interface TabOption {
  label: string;
  value: number | string;
}

export default defineComponent({
  name: 'Tab',
  props: {
    modelValue: {
      type: [Number, String],
      required: true,
    },
    options: {
      type: Array as PropType<TabOption[]>,
      default: () => [],
    },
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const handleTabClick = (value: number | string) => {
      emit('update:modelValue', value);
    };

    return () => (
      <div class={styles.container}>
        {props.options.map((tab) => (
          <div
            class={[
              styles.tabItem,
              props.modelValue === tab.value ? styles.active : '',
            ]}
            key={tab.value}
            onClick={() => handleTabClick(tab.value)}
          >
            {tab.label}
          </div>
        ))}
      </div>
    );
  },
});
