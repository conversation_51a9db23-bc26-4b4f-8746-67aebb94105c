import { requestClient } from '#/api/request';

// 获取基本信息数据
export async function getBasicInfoData(data) {
  return requestClient.post(
    '/rgdc-search/dSubstanceBase/getSubstanceInfo',
    data,
  );
}

// 获取计算性质信息数据
export async function getCalculateInfoData(data) {
  return requestClient.post(
    '/rgdc-search/dSubstanceCalculate/getSubstanceCalculateInfo',
    data,
  );
}

// 获取物化性质信息数据
export async function getPhysicalInfoData(data) {
  return requestClient.post(
    '/rgdc-search/dSubstancePhysical/getSubstancePhysicalInfo',
    data,
  );
}

// 获取危险识别信息数据
export async function getHazardInfoData(data) {
  return requestClient.post(
    '/rgdc-search/dSubstanceHazard/getSubstanceHazardInfo',
    data,
  );
}

// 获取毒性信息数据
export async function getToxicityInfoData(data) {
  return requestClient.post(
    '/rgdc-search/dSubstanceToxicity/getSubstanceToxicityInfo',
    data,
  );
}

// 获取用途信息数据
export async function getPurposeInfoData(data) {
  return requestClient.post(
    '/rgdc-search/dSubstancePurposeInfo/getSubstancePurposeInfo',
    data,
  );
}

// 物质数据   Srart ///////////////////////////////////////////////////////////////////////////////////////
// 物质数据-上下游信息
export async function getUpDownInfoByInchikey(data) {
  return requestClient.post(
    '/rgdc-search/dSubstanceUpDownInfo/getOneByInchikey',
    data,
  );
}

// 物质数据-分类信息
export async function getDSubstanceClassificationTreeByInchikey(data) {
  return requestClient.post(
    '/rgdc-search/dSubstanceClassification/getDSubstanceClassificationTreeByInchikey',
    data,
  );
}

// 物质数据-图谱信息
export async function getToxicityInfoByInchikey(data) {
  return requestClient.post(
    '/rgdc-search/dSubstanceToc/getListByInchikey',
    data,
  );
}

// 物质数据-图谱信息-光谱详情
export async function getToxicityInfoSpectralByTocId(data) {
  return requestClient.post(
    '/rgdc-search/dSubstanceTocSpectral/getOneByTocId',
    data,
  );
}


// 物质数据-图谱信息-商家信息
export async function getDSubstanceBusinessInfoListByInchikey(data) {
  return requestClient.post('/rgdc-search/dSubstanceBusinessInfo/listByPage', data);
}

export async function getDSubstanceBusinessInfoDetailsListByPage(data: any) {
  return requestClient.post<any[]>('/rgdc-search/dSubstanceBusinessInfoDetails/listByPage', data);
}

// 获取基本信息
// 基本信息 - BasicInfo.vue
// 结构 - Structure.vue
// 计算性质 - CalculateInfo.vue
// 物化性质 - PhysicalInfo.vue
// 毒性 - Toxicity.vue
// 用途信息 - PurposeInfo.vue
export async function getBasicCombinedInfo(data: any) {
  return requestClient.post<any>('/rgdc-search/dSubstanceBase/getBasicCombinedInfo', data);
}


// 物质数据   End// ///////////////////////////////////////////////////////////////////////////////////////

// 结构信息数据   Start ///////////////////////////////////////////////////////////////////////////////////////
// 结构信息数据-结构信息
export async function getStructureInfoData(data) {
  return requestClient.post(
    '/rgdc-search/dSubstanceStructure/getOneByInchikey',
    data,
  );
}
// 结构信息数据-结构信息  End ///////////////////////////////////////////////////////////////////////////////////////

// 获取反应信息
export async function getReactionInfoData(data) {
  return requestClient.post(
    '/rgdc-search/dwdReactionBase/listByPage',
    data,
  );
}

// 获取多步反应详细信息
export async function getReactSchemeInfo(data) {
  return requestClient.post(
    '/rgdc-search/dwdReactionBase/getReactSchemeInfo',
    data,
  );
}

// 获取反应文献信息
export async function getReferenceInfo(data) {
  return requestClient.post(
    '/rgdc-search/dwdReactionBase/getReferenceInfo',
    data,
  );
}

// 获取气相信息
export async function getGasPhaseInfo(data) {
  return requestClient.post(
    '/rgdc-search/dwdThermGpParameters/getGasPhaseInfo',
    data,
  );
}

// 获取气相相变液体恒压热容信息
export async function getGasConstantPressureInfo(data) {
  return requestClient.post(
    '/rgdc-search/dwdThermGpConstantPressureHeatCapacity/getGasConstantPressureInfo',
    data,
  );
}

// 获取气相热容肖马特方程信息
export async function getGpHeatCapacityShomateEquationInfo(data) {
  return requestClient.post(
    '/rgdc-search/dwdThermGpHeatCapacityShomateEquation/getGpHeatCapacityShomateEquationInfo',
    data,
  );
}

// 获取凝聚相信息
export async function getCondensedPhaseInfo(data) {
  return requestClient.post(
    '/rgdc-search/dwdThermCpParameters/getCondensedPhaseInfo',
    data,
  );
}

// 获取凝聚相相变液体恒压热容信息
export async function getLiquidInfo(data) {
  return requestClient.post(
    '/rgdc-search/dwdThermCpConstantPressureHeatCapacity/getLiquidInfo',
    data,
  );
}

// 获取相变信息
export async function getPhaseChangeInfo(data) {
  return requestClient.post(
    '/rgdc-search/dwdThermPcParameters/getPhaseChangeInfo',
    data,
  );
}

// 获取相变汽化焓信息
export async function getEnthalpyOfVaporizationInfo(data) {
  return requestClient.post(
    '/rgdc-search/dwdThermPcEnthalpyOfVaporization/getEnthalpyOfVaporizationInfo',
    data,
  );
}

// 获取相变汽化熵信息
export async function getEntropyOfVaporizationInfo(data) {
  return requestClient.post(
    '/rgdc-search/dwdThermPcEntropyOfVaporization/getEntropyOfVaporizationInfo',
    data,
  );
}

// 获取相变汽化焓方程参数信息
export async function getEnthalpyParametersInfo(data) {
  return requestClient.post(
    '/rgdc-search/dwdThermPcEnthalpyEquationParameters/getEnthalpyParametersInfo',
    data,
  );
}

// 获取相变安托万方程参数信息
export async function getAntoineParametersInfo(data) {
  return requestClient.post(
    '/rgdc-search/dwdThermPcAntoineEquationParameters/getAntoineParametersInfo',
    data,
  );
}

// 获取相变熔化焓信息
export async function getEnthalpyOfFusionInfo(data) {
  return requestClient.post(
    '/rgdc-search/dwdThermPcEnthalpyOfFusion/getEnthalpyOfFusionInfo',
    data,
  );
}

// 获取相变融合熵信息
export async function getEntropyFusionInfo(data) {
  return requestClient.post(
    '/rgdc-search/dwdThermPcEntropyOfFusion/getEntropyFusionInfo',
    data,
  );
}

// 获取相变焓信息
export async function getEnthalpyTransitionInfo(data) {
  return requestClient.post(
    '/rgdc-search/dwdThermPcEnthalpyOfPhaseTransition/getEnthalpyTransitionInfo',
    data,
  );
}

// 获取相变熵信息
export async function getEntropyTransitionInfo(data) {
  return requestClient.post(
    '/rgdc-search/dwdThermPcEntropyOfPhaseTransition/getEntropyTransitionInfo',
    data,
  );
}

// 获取相变升华焓信息
export async function getEnthalpySublimationInfo(data) {
  return requestClient.post(
    '/rgdc-search/dwdThermPcEnthalpyOfSublimation/getEnthalpySublimationInfo',
    data,
  );
}

// 获取相变升华熵信息
export async function getEntropySublimationInfo(data) {
  return requestClient.post(
    '/rgdc-search/dwdThermPcEntropyOfSublimation/getEntropySublimationInfo',
    data,
  );
}

// 获取亨利定律常数（水溶液）信息
export async function getHlConstantWaterSolutionInfo(data) {
  return requestClient.post(
    '/rgdc-search/dwdThermHlConstantWaterSolution/getHlConstantWaterSolutionInfo',
    data,
  );
}

// 获取相变减压沸点信息
export async function getReducedPressureBoilingPointInfo(data) {
  return requestClient.post(
    '/rgdc-search/dwdThermPcReducedPressureBoilingPoint/getReducedPressureBoilingPointInfo',
    data,
  );
}

// 获取相变温度信息
export async function getTemperatureOfPhaseTransitionInfo(data) {
  return requestClient.post(
    '/rgdc-search/dwdThermPcTemperatureOfPhaseTransition/getTemperatureOfPhaseTransitionInfo',
    data,
  );
}

// 获取双原子分子常数中使用的符号信息
export async function getCodmDiatomicSymbolsUsedInfo(data) {
  return requestClient.post(
    '/rgdc-search/dwdThermCodmDiatomicSymbolsUsed/getCodmDiatomicSymbolsUsedInfo',
    data,
  );
}

// 获取双原子分子常数12c16o信息
export async function getCodmDiatomicConstantsFor12c16oInfo(data) {
  return requestClient.post(
    '/rgdc-search/dwdThermCodmDiatomicConstantsFor12c16o/getCodmDiatomicConstantsFor12c16oInfo',
    data,
  );
}

// 热力学数据   Start ///////////////////////////////////////////////////////////////////////////////////////
// 热力学数据-引用信息
export async function getReferencesData(data) {
  return requestClient.post(
    '/rgdc-search/dwdThermReferences/getList',
    data,
  );
}
// 热力学数据-说明信息
export async function getNotesData(data) {
  return requestClient.post(
    '/rgdc-search/dwdThermNotes/getList',
    data,
  );
}
// 热力学数据  End ///////////////////////////////////////////////////////////////////////////////////////

// 获取方法代码信息
export async function getMethodData() {
  return requestClient.post(
    '/rgdc-search/dwdThermMethod/getDwdThermMethodInfo'
  );
}
