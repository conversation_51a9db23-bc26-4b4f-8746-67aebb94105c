import { getDictItems } from '#/api';
import {
  dataAuthApply,
  getAllPCodes,
} from '#/views/modules/tDataPermission/api.ts';
import {
  DatePicker,
  Dialog,
  Form,
  FormItem,
  Input,
  MessagePlugin,
  Select,
  Textarea,
} from 'tdesign-vue-next';
import { defineComponent, onMounted, reactive, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';

import { useApprovalModal } from '../../hooks/useApprovalModal';

export default defineComponent({
  name: 'ApprovalModalProvider',
  setup() {
    const { isVisible, currentItem, closeModal, onSuccessCallback } =
      useApprovalModal();
    const form = ref();
    const formData = ref<any>({});
    const resourceTypeList = ref();
    const permissionTypeList = ref();
    const resourcesType2 = ref();
    const isOffline = ref(false);

    const FORM_RULES = {
      permissionType: [
        {
          required: true,
          message: '请选择权限类型',
        },
      ],
      startTime: [
        {
          required: true,
          message: '请选择权限生效时间',
        },
      ],
      endTime: [
        {
          required: true,
          message: '请选择权限失效时间',
        },
      ],
      applicant_name: [
        {
          required: true,
          message: '请输入申请人姓名',
        },
      ],
      phone_number: [
        {
          required: true,
          message: '请输入手机号',
        },
      ],
      applicant_email: [
        {
          required: true,
          message: '请输入邮箱',
        },
      ],
    };

    const state = reactive({
      tagObj: {},
    });

    const initStatus = () => {
      state.tagObj = {};
      formData.value = {};
    };

    const reqRunner = {
      dataAuthApply: useRequest(dataAuthApply, {
        manual: true,
        debounceWait: 300,
        onError: () => {},
        onSuccess: (_res: any) => {
          initStatus();
          closeModal();
          MessagePlugin.success('申请成功，请等待审批');
          onSuccessCallback.value?.();
        },
      }),
    };

    const formatDate = (date: Date): string => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    };

    const submitApproval = async () => {
      if (!currentItem.value) return;
      const vali = await form.value.validate();
      if (vali === true) {
        reqRunner.dataAuthApply.run({
          ...state.tagObj,
          ...formData.value,
        });
      }
    };

    const onOpen = async () => {
      console.log(currentItem.value);
      if (currentItem.value) {
        resourcesType2.value = await getDictItems('UNSTRUCTURED_DATA_TYPE');
        state.tagObj = currentItem.value;
        formData.value = currentItem.value;
        isOffline.value = currentItem.value.is_offline === '1';
        resourceTypeList.value = resourcesType2.value.some(
          (item) => item.value === formData.value.dataType,
        )
          ? [{ label: '文件', value: '**unstructured_file**' }]
          : await getAllPCodes(formData.value.templateCode);
        const currentDate = new Date();
        formData.value.startTime = formatDate(currentDate);
        const currentDate_7d = new Date();
        currentDate_7d.setDate(currentDate_7d.getDate() + 7);
        formData.value.endTime = formatDate(currentDate_7d);
      }
    };

    onMounted(async () => {
      permissionTypeList.value = await getDictItems('DATA_PERMISSION_TYPE');
    });

    return () => (
      <Dialog
        class="custom-edit-modal"
        header="数据权限申请"
        onClose={closeModal}
        onConfirm={submitApproval}
        onOpened={onOpen}
        style="max-width: 700px; width: 90vw"
        v-model:visible={isVisible.value}
      >
        <Form
          class="w-full"
          data={formData.value}
          label-align="right"
          ref={form}
          rules={FORM_RULES}
        >
          <div class="grid w-full grid-cols-1 gap-3">
            {isOffline.value && (
              <>
                <FormItem label="申请人姓名" name="applicant_name">
                  <Input
                    placeholder="请输入申请人姓名"
                    v-model={formData.value.applicant_name}
                  />
                </FormItem>
                <FormItem label="机构名称" name="corporate_name">
                  <Input
                    placeholder="请输入机构名称"
                    v-model={formData.value.corporate_name}
                  />
                </FormItem>
                <FormItem label="职业" name="career">
                  <Input
                    placeholder="请输入职业"
                    v-model={formData.value.career}
                  />
                </FormItem>
                <FormItem label="手机号" name="phone_number">
                  <Input
                    placeholder="请输入手机号"
                    v-model={formData.value.phone_number}
                  />
                </FormItem>
                <FormItem label="邮箱" name="applicant_email">
                  <Input
                    placeholder="请输入邮箱"
                    v-model={formData.value.applicant_email}
                  />
                </FormItem>
              </>
            )}
            <FormItem label="权限类型" name="permissionType">
              <Select
                clearable
                options={permissionTypeList.value}
                placeholder="请选择"
                v-model={formData.value.permissionType}
              />
            </FormItem>
            <FormItem label="权限生效时间" name="startTime">
              <DatePicker
                allow-input
                clearable
                enable-time-picker
                v-model={formData.value.startTime}
              />
            </FormItem>
            <FormItem label="权限失效时间" name="endTime">
              <DatePicker
                allow-input
                clearable
                enable-time-picker
                v-model={formData.value.endTime}
              />
            </FormItem>
            <FormItem label="备注" name="remark">
              <Textarea
                placeholder="若有其他说明，请填写备注"
                v-model={formData.value.remark}
              />
            </FormItem>
          </div>
        </Form>
      </Dialog>
    );
  },
});
