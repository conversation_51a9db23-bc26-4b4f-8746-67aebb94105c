import type { ListItem } from './usePermissions'; // 复用类型

import { reactive, toRefs } from 'vue';

// 使用 reactive 创建一个单一的状态对象，这有助于在逻辑上将相关状态组合在一起，并可能解决一些响应性问题
const state = reactive({
  isVisible: false,
  currentItem: null as ListItem | null,
  onSuccessCallback: null as (() => void) | null,
});

export function useApprovalModal() {
  // 定义打开弹窗的函数
  const openModal = (item: ListItem, onSuccess?: () => void) => {
    // 每次打开都更新为当前项的数据和回调，保证数据精准
    state.currentItem = {
      ...item,
      dataName: item.dataName || '',
    };
    state.onSuccessCallback = onSuccess || null;
    state.isVisible = true;
  };

  // 定义关闭弹窗的函数
  const closeModal = () => {
    state.isVisible = false;
    // 关闭时清理数据，避免内存占用
    state.currentItem = null;
    state.onSuccessCallback = null;
  };

  // 暴露状态和方法
  // 使用 toRefs 将 reactive 对象的属性转换为 ref，这样在消费端解构时仍能保持响应性
  return {
    ...toRefs(state),
    openModal,
    closeModal,
  };
}
