<script setup lang="tsx">
import type { StatisticItem } from '#/components/statistics-overview/index.vue';
import type { HomeListSection } from '#/views/modules/new_version/components/index';

import { getDictItems } from '#/api';
import { isLogin } from '#/components/notLoginGo';
import StatisticsOverview from '#/components/statistics-overview/index.vue';
import { useSearchStore } from '#/store/search';
import {
  HomeList,
  HomeSearch,
} from '#/views/modules/new_version/components/index';
import DataCategory from '#/views/modules/new_version/home/<USER>/DataCategory.vue';
import DataTools from '#/views/modules/new_version/home/<USER>/DataTools.vue';
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import {
  getResourcesTypeWithCount,
  getServiceHome,
  getStatistics,
} from './api';

const searchStore = useSearchStore();
const router = useRouter();
const homeListRef = ref<{ scrollTo: (anchor: string) => void }>();

const statisticsData = ref<StatisticItem[]>([]);
const categoryList = ref([]);
const toolsList = ref<any[]>([]);

const initHomeDisplayList = () => {
  getResourcesTypeWithCount({}).then((res) => {
    categoryList.value = res.filter((el) => el.isDisplay === '1');
  });
};

const handleCategoryMore = () => {
  // TODO: 实现数据分类更多功能
};

const handleCategoryClick = (item: any) => {
  isLogin().then(() => {
    searchStore.setSelectedSearchType(item.value);
    router.push('/search-page');
  });
};

const handleToolsMore = () => {
  // TODO: 实现数据工具更多功能
};

const handleToolClick = (_item: any) => {
  const { tid } = _item;
  isLogin().then(() => {
    router.push({
      path: `/data-tools-detail/${tid}`,
    });
  });
};

const handleStatisticClick = (_item: StatisticItem) => {
  // 这里可以添加跳转到具体统计页面的逻辑
};

function initStatisticsData() {
  Promise.all([
    getStatistics({
      esIndex: import.meta.env.VITE_ES_INDEX,
    }),
    getDictItems('HOME_STATISTICS'),
  ]).then((res) => {
    const [statisticsDataRes, dictItems] = res;
    const newStatisticsData = dictItems.map((item, index) => {
      const statistic = statisticsDataRes.find(
        (statistic) => statistic.value === item.value,
      );
      const colors = [
        '#7c3aed',
        '#ec4899',
        '#06b6d4',
        '#10b981',
        '#f59e0b',
        '#84cc16',
      ];
      const iconMap: any = {
        dataLiterature: 'FileTextIcon',
        dataset: 'FolderIcon',
        storageCapacity: 'HardDriveIcon',
        dataTool: 'ToolIcon',
      };
      return {
        ...statistic,
        key: statistic.value,
        title: statistic.label,
        value: statistic.count || 0,
        unit: statistic.unit,
        icon: iconMap[statistic.value] || 'FolderIcon',
        color: colors[index % colors.length] || '#7c3aed',
      };
    });
    statisticsData.value = newStatisticsData;
  });
}

const initServiceHome = () => {
  getServiceHome().then((res) => {
    toolsList.value = res;
  });
};

const handleAnchorClick = (anchor: string) => {
  homeListRef.value?.scrollTo(anchor);
};

const homeSections = computed<HomeListSection[]>(() => [
  {
    type: 'single',
    key: 'c',
    content: () => (
      <StatisticsOverview
        data={statisticsData.value}
        onCardClick={handleStatisticClick}
      />
    ),
  },
  {
    type: 'row',
    children: [
      {
        key: 'b',
        class: 'category-col',
        content: () => (
          <DataCategory
            list={categoryList.value}
            onCardClick={handleCategoryClick}
            onMore={handleCategoryMore}
            title="数据分类"
          />
        ),
      },
      {
        key: 'd',
        class: 'tools-col',
        content: () => (
          <DataTools
            list={toolsList.value}
            onMore={handleToolsMore}
            onToolClick={handleToolClick}
            title="数据工具"
          />
        ),
      },
    ],
  },
]);

onMounted(() => {
  searchStore.setInitCategory(0);
  initHomeDisplayList();
  initStatisticsData();
  initServiceHome();
});

onUnmounted(() => {});
</script>

<template>
  <div class="home-container">
    <!-- <Cover /> -->
    <HomeSearch @anchor-click="handleAnchorClick" />
    <HomeList ref="homeListRef" :sections="homeSections" />
  </div>
</template>

<style scoped>
.home-container {
  width: 100%;
  box-sizing: border-box;
}

.image-layout-section {
  margin-top: 40px;
}

/* 为统计概览组件调整边距 */
.home-container > :deep(.statistics-overview) {
  margin: 20px 0;
}

.category-tools-row {
  display: flex;
  gap: 40px;
  margin-top: 16px;
  margin-bottom: 32px;
  align-items: stretch;
  padding: 0 48px;
}
.category-col {
  flex: 2;
  min-width: 0;
}
.tools-col {
  flex: 1;
  min-width: 340px;
}

:deep(.data-category),
:deep(.data-tools) {
  height: 100%;
  min-height: 420px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
:deep(.data-category .card-list) {
  min-height: 320px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .home-container {
  }
  .category-tools-row {
    gap: 20px;
  }
  .tools-col {
    min-width: 0;
  }
}
@media (max-width: 900px) {
  .category-tools-row {
    flex-direction: column;
    gap: 24px;
    margin-top: 24px;
  }
  .tools-col {
    min-width: 0;
  }
  :deep(.data-category),
  :deep(.data-tools) {
    min-height: 320px;
  }
}
@media (max-width: 480px) {
  .home-container {
    padding: 0 4px;
  }
  .category-tools-row {
    flex-direction: column;
    margin-top: 4px;
    gap: 6px;
    padding: 0 1px;
    margin-bottom: 8px;
  }
  .category-col,
  .tools-col {
    min-width: 0;
    padding: 0;
    width: 100%;
  }
  :deep(.data-category),
  :deep(.data-tools) {
    min-height: 120px;
    padding: 4px 0;
  }
  :deep(.data-category .card-list),
  :deep(.data-tools .card-list) {
    display: flex;
    flex-direction: column;
    gap: 6px;
    min-height: 60px;
  }
  :deep(.data-category .card-list .t-card),
  :deep(.data-tools .card-list .t-card) {
    width: 100% !important;
    margin: 0 auto;
    font-size: 12px;
    padding: 8px 4px;
  }
  :deep(.t-card__title) {
    font-size: 14px;
  }
  :deep(.statistics-overview) {
    margin: 8px 0;
  }
  :deep(.search-box) {
    margin-bottom: 6px;
  }
  :deep(.t-card img),
  :deep(.data-category .card-list img),
  :deep(.data-tools .card-list img) {
    width: 36px !important;
    height: 36px !important;
  }
  /* 统计卡片紧凑化 */
  :deep(.t-card) {
    width: 100% !important;
    margin: 0 auto 10px auto !important;
    padding: 12px 6px 10px 6px !important;
    border-radius: 8px !important;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03) !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    min-height: 90px !important;
  }
  :deep(.t-card__icon) {
    width: 28px !important;
    height: 28px !important;
    margin-bottom: 6px !important;
  }
  :deep(.t-card__title) {
    font-size: 13px !important;
    color: #374151 !important;
    margin-bottom: 2px !important;
    text-align: center !important;
    font-weight: 500 !important;
  }
  :deep(.t-card__value) {
    font-size: 18px !important;
    font-weight: bold !important;
    color: #2196f3 !important;
    margin-bottom: 0 !important;
    text-align: center !important;
  }
  :deep(.t-card__unit) {
    font-size: 12px !important;
    color: #6b7280 !important;
    margin-left: 2px !important;
  }
}
</style>
