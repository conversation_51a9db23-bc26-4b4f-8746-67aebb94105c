import type { PropType } from 'vue';

import Collection from '#/components/collection/index.vue';
import Share from '#/components/share/index.vue';
import { defineComponent } from 'vue';

import styles from './card-item.module.less';

// 定义 ListItem 类型，确保 listData 类型安全
interface ListItem {
  id: number | string;
  // 基本信息
  dataName?: string; // 数据名称
  dataImg?: string; // 数据图片
  dataDescri?: string; // 数据描述
  author?: string; // 作者
  cas?: string; // CAS号
  molecularFormula?: string; // 分子式
  categoryName?: string; // 分类名称
  browsingCount?: number; // 浏览次数
  baseCode?: string; // 基础代码，用于详情和下载
  fileType?: string; // 文件类型

  // 时间和状态
  createTime: string;
  isFavorite: boolean;
  isVisible?: number | string; // 审批状态：1-审批通过，0-待审批

  // 标签和分类
  tags: string[];
  labelName?: string; // 标签名称（逗号分隔）

  // 数据类型和权限
  dataType_text?: string;
  dataAuth_text?: string;
  dataType?: number | string;
  dataAuth?: number | string;
}

export default defineComponent({
  name: 'CardItem',
  props: {
    item: {
      type: Object as PropType<ListItem>,
      required: true,
    },
  },
  emits: ['itemClick', 'collectionChange'],
  setup(props, { emit }) {
    const handleItemClick = () => {
      emit('itemClick', props.item);
    };

    const onCollectionChange = (event: { isFavorite: boolean; row: any }) => {
      emit('collectionChange', props.item, event);
    };

    return () => (
      <div class={styles['card-item']} onClick={handleItemClick}>
        <div class={styles['card-img']}>
          <img alt={props.item.dataName} src={props.item.dataImg} />
          <div class={styles['card-tags-overlay']}>
            <div
              class={styles['card-type-tag']}
              style={{
                backgroundColor: '#007bff', // Example color
              }}
            >
              {props.item.dataType_text}
            </div>
          </div>
        </div>
        <div class={styles['card-content']}>
          <div class={styles['card-title']}>{props.item.dataName}</div>
          <div class={styles['card-meta']}>
            <span>关键词: {props.item.labelName}</span>
            <span>所属分类: {props.item.categoryName}</span>
            <span>作者: {props.item.author}</span>
          </div>
          <div class={styles['card-desc']}>{props.item.dataDescri}</div>
        </div>
        <div class={styles['card-actions']}>
          <span>{props.item.createTime}</span>
          <Collection
            isFavorite={props.item.isFavorite}
            onCollectionChange={onCollectionChange}
            row={props.item}
          />
          <Share row={props.item} />
          <span>
            <i class="t-icon t-icon-browse" />
            {props.item.browsingCount}
          </span>
        </div>
      </div>
    );
  },
});
