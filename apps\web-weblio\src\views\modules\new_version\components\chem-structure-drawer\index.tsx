import { Dialog } from 'tdesign-vue-next';
import { computed, defineComponent, ref, watch } from 'vue';

import Ketcher from './ketcher';

export default defineComponent({
  name: 'ChemStructureDrawer',
  props: {
    modelValue: {
      type: Object,
      required: true,
    },
    visible: {
      type: Boolean,
      required: true,
    },
  },
  emits: ['update:visible', 'update:modelValue', 'ok', 'cancel'],
  setup(props, { emit }) {
    const localValue = ref(props.modelValue);
    const isVisible = computed(() => props.visible);

    watch(
      () => props.modelValue,
      (val) => {
        localValue.value = val;
      },
    );

    const handleOk = () => {
      emit('update:modelValue', localValue.value);
      emit('ok', localValue.value);
      emit('update:visible', false);
      localValue.value = {};
    };

    const handleCancel = () => {
      emit('cancel');
      emit('update:visible', false);
    };

    return () => (
      <Dialog
        header="化学式查询"
        onClose={handleCancel}
        onConfirm={handleOk}
        v-model:visible={isVisible.value}
      >
        <Ketcher
          modelValue={localValue.value}
          onUpdate:modelValue={(val) => (localValue.value = val)}
          style="height: 400px; width: 600px"
        />
      </Dialog>
    );
  },
});
