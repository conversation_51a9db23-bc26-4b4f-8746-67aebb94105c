import { useAccessStore } from '@vben/stores';
import { defineComponent } from 'vue';

import { LoggedList, UnLoggedList } from './modules/index';

import styles from './style.module.less';

export default defineComponent({
  name: 'SearchList',
  setup() {
    const accessStore = useAccessStore();
    const isLoggedIn = !!accessStore.accessToken;

    return () => (
      <div class={styles.container}>
        {isLoggedIn ? <LoggedList /> : <UnLoggedList />}
      </div>
    );
  },
});
