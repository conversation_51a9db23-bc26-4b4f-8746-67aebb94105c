<script setup lang="ts">
import {
  HeartIcon,
  ShareIcon,
  ViewListIcon,
  ViewModuleIcon,
  CalculatorIcon,
  ChevronLeftIcon
} from 'tdesign-icons-vue-next';
import {
  Button,
  Checkbox,
  CheckboxGroup,
  Empty,
  Loading,
  Pagination,
  Select,
  Table,
  Tag,
  Dialog
} from 'tdesign-vue-next';
import { computed, h, onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';

import {
  getHlConstantWaterSolutionInfo
} from '../api';
import { useSearchStore } from '#/store/search';

interface HlConstantWaterSolutionInfo {
  formula: string;
  kh: string;
  d: string;
  method: string;
  reference: string;
  comment: string;
}

/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * 当前详情数据
   */
  detailItem: {},
  /**
  * 加载状态
  */
  loading: true,

});
// 响应式数据
const hlConstantList = ref<HlConstantWaterSolutionInfo[]>([]);

// 跳转到References组件的函数
const navigateToReferences = () => {
  // 通过点击菜单项来切换到References标签页
  setTimeout(() => {
    // 查找包含"引用"文本的菜单项
    const referenceMenuItem = Array.from(document.querySelectorAll('[class*="menu-item-title"]')).find(el =>
      el.textContent?.includes('引用')
    ) || Array.from(document.querySelectorAll('[class*="menu-item"]')).find(el =>
      el.textContent?.includes('引用')
    );

    if (referenceMenuItem) {
      // 如果找到了引用菜单项，模拟点击
      (referenceMenuItem as HTMLElement).click();
    }
  }, 100);
};

// 表格列定义
const getHlConstantWaterSolutionColumns = () => [
  {
    colKey: 'id',
    title: 'ID',
  },
  {
    colKey: 'kh',
    title: (h) => h('div', [
      h('div', ['k°', h('sub', 'H'),
        '(mol/(kg*bar))'])
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.kh })
  },
  {
    colKey: 'd',
    title: (h) => h('div', [
      h('div', ['d(ln(k', h('sub', 'H'),
        '))/d(1/T) (K)'])
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.d })
  },
  {
    colKey: 'method',
    title: 'Method',
    width: 80,
    ellipsis: true,
    cell: (h, { row }) => {
      return h('a', {
        innerHTML: row.method,
        style: 'color: #1890ff; cursor: pointer; text-decoration: underline;',
        onClick: (e) => {
          e.preventDefault();
          showMethodCodes();
        }
      });
    }
  },
  {
    colKey: 'reference',
    title: 'Reference',
    width: 300,
    ellipsis: true,
    cell: (h, { row }) => h('a', {
      innerHTML: row.reference,
      style: 'color: #1890ff; cursor: pointer; text-decoration: underline;',
      onClick: (e) => {
        e.preventDefault();
        navigateToReferences();
      }
    })
  },
  {
    colKey: 'comment',
    title: 'Comment',
    width: 500,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.comment })
  }
];

// Store
const searchStore = useSearchStore();
const router = useRouter();

// 显示方法代码说明的函数
const showMethodCodes = () => {
  // 使用路由跳转到Method页面
  sessionStorage.setItem('targetKey', 'hlConstantInfo');
  router.push({ name: 'MethodCodes' });
};
// 获取当前详情数据
state.detailItem = computed(() => {
  // 获取当前详情数据
  const detail = searchStore.currentDetailItem;
  // 如果当前详情数据存在，则返回当前详情数据
  if (detail) {
    // 返回当前详情数据
    return detail;
  } else {
    // 如果当前详情数据不存在，则从本地存储中获取当前详情数据
    const local = localStorage.getItem('currentDetailItem');
    // 如果本地存储中不存在当前详情数据，则返回空对象
    return JSON.parse(local ?? '{}');
  }
});

const displayColumns = ref(['kh', 'd', 'method', 'reference', 'comment']);

onMounted(async () => {
  try {
    // 调用相变信息查询API
    const response = await getHlConstantWaterSolutionInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    hlConstantList.value = response;
  } catch (error) {
    console.error('获取亨利定律信息失败:', error);
  } finally {
    // 关闭加载状态
    state.loading = false;
  }
});
</script>

<template>
  <div class="hlConstant-info">
    <div v-if="state.loading" class="loading-container">
      <div class="loading-spinner">加载亨利定律信息中...</div>
    </div>
    <template v-else>
      <div v-if="hlConstantList && hlConstantList.length > 0">
        <h1>Henry's Law data</h1>
        <h2>Henry's Law constant (water solution)</h2>
        <div class="formula" v-if="hlConstantList[0]">
          <div v-html="hlConstantList[0].formula"></div>
        </div>
        <Table :data="hlConstantList" :columns="getHlConstantWaterSolutionColumns()" :displayColumns="displayColumns"
          :bordered="true" :hover="true" :stripe="true" row-key="id" table-layout="fixed" cell-empty-content="-"
          style="margin-top: 10px;" />
      </div>
    </template>
  </div>
</template>

<style scoped lang="scss">
.hlConstant-info {

  // 加载状态样式
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;

    .loading-spinner {
      font-size: 16px;
      color: #666;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        right: -20px;
        top: 50%;
        transform: translateY(-50%);
        width: 12px;
        height: 12px;
        border: 2px solid #e8e8e8;
        border-top: 2px solid #1890ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }

    @keyframes spin {
      0% {
        transform: translateY(-50%) rotate(0deg);
      }

      100% {
        transform: translateY(-50%) rotate(360deg);
      }
    }
  }

  h1 {
    font-size: 24px;
    margin-bottom: 10px;
  }

  h2 {
    font-size: 20px;
    margin-bottom: 10px;
  }

  .formula {
    line-height: 1.6;
    font-size: 14px;
    margin-bottom: 10px;
  }

  :deep(.t-table td),
  :deep(.t-table th) {
    border-right: 1px solid #e6e8eb !important;
  }

  :deep(.t-table td:last-child),
  :deep(.t-table th:last-child) {
    border-right: none !important;
  }

  :deep(.t-table td) {
    max-height: 2.8em !important;
    line-height: 1.4 !important;
    padding: 8px !important;
    overflow: hidden !important;
    word-break: break-word !important;
  }
}
</style>
