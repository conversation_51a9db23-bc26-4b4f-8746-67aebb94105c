import type { PropType } from 'vue';

import { defineComponent } from 'vue';

import CardItem from '../card-item/card-item';

import styles from './unlogged-list.module.less';

interface ListItem {
  id: number | string;
  dataName?: string;
  dataImg?: string;
  dataDescri?: string;
  author?: string;
  cas?: string;
  molecularFormula?: string;
  categoryName?: string;
  browsingCount?: number;
  baseCode?: string;
  fileType?: string;
  createTime: string;
  isFavorite: boolean;
  isVisible?: number | string;
  tags: string[];
  labelName?: string;
  dataType_text?: string;
  dataAuth_text?: string;
  dataType?: number | string;
  dataAuth?: number | string;
}

export default defineComponent({
  name: 'UnloggedList',
  props: {
    item: {
      type: Object as PropType<ListItem>,
      required: true,
    },
  },
  emits: ['login'],
  setup(props, { emit }) {
    const handleLogin = () => {
      emit('login');
    };

    return () => (
      <div class={styles.container}>
        <CardItem item={props.item} />
        <div class={styles.mask}>
          <div class={styles['login-prompt']}>登录授权后，可以查看更多数据</div>
          <button class={styles['login-button']} onClick={handleLogin}>
            查看更多
          </button>
        </div>
      </div>
    );
  },
});
