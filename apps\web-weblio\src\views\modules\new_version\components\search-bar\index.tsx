import { getDictItems } from '#/api';
import { defineComponent, onMounted, ref } from 'vue';

import { AIDialog, ChemStructureDrawer } from '..';
import {
  useAiAnswerModal,
  useChemStructureDrawer,
  useSearchNavigation,
} from '../../hooks/index';
import { KeyWords, SearchInput, Tab } from './modules/index';

import styles from './style.module.less';

export default defineComponent({
  name: 'SearchBar',
  setup() {
    const activeTab = ref<string>('0');
    const searchWord = ref('');
    const { handleSearch, goToAdvancedSearch } = useSearchNavigation();
    const { openModal: openAiModal } = useAiAnswerModal();
    const {
      isVisible: isDrawerVisible,
      structure,
      openDrawer,
      handleSuccess,
    } = useChemStructureDrawer();
    const tabOptions = ref([]);
    onMounted(async () => {
      const res = await getDictItems('HOME_SEARCH');
      tabOptions.value = res.map((item) => ({
        label: item.label,
        value: item.value,
      }));
    });

    const handleKeywordChange = (keyword: string) => {
      searchWord.value = keyword;
    };

    const onSearch = () => {
      handleSearch({
        queryType: activeTab.value,
        searchContent: searchWord.value,
      });
    };

    const onAiSearch = () => {
      openAiModal((result) => {
        console.warn(result);
      });
    };

    const onAdvancedSearch = () => {
      goToAdvancedSearch();
    };

    const onStructureSearch = () => {
      openDrawer((data) => {
        searchWord.value = data.smiles;
      });
    };

    return () => (
      <div class={styles.container}>
        <div class={styles.header}>
          <Tab
            modelValue={activeTab.value}
            onUpdate:modelValue={(val) => (activeTab.value = val)}
            options={tabOptions.value}
          />
        </div>
        <div class={styles.searchBox}>
          <SearchInput
            onAiSearch={onAiSearch}
            onSearch={onSearch}
            onUpdate:value={(val) => (searchWord.value = val)}
            value={searchWord.value}
          />
          <div class={styles.searchButtons}>
            <div class={styles.searchButton} onClick={onAdvancedSearch}>
              高级检索 <span class={styles.arrow}>{'>'}</span>
            </div>
            <div class={styles.searchButton} onClick={onStructureSearch}>
              结构式检索 <span class={styles.arrow}>{'>'}</span>
            </div>
          </div>
        </div>
        <div class={styles.footer}>
          <KeyWords onChange={handleKeywordChange} />
        </div>
        <AIDialog />
        <ChemStructureDrawer
          modelValue={structure.value}
          onOk={handleSuccess}
          onUpdate:visible={(val) => (isDrawerVisible.value = val)}
          visible={isDrawerVisible.value}
        />
      </div>
    );
  },
});
