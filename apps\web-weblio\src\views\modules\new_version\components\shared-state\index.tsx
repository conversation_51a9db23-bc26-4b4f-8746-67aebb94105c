import { useUserStore } from '@vben/stores';
import { LockOffIcon, LockOnIcon } from 'tdesign-icons-vue-next';
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'SharedState',
  props: {
    item: {
      type: Object,
      required: true,
    },
  },
  setup(props) {
    const userStore = useUserStore();

    const isShowLockIcons = (item) => {
      const { dataAuth } = item;
      const sourceSystemCode = (userStore.userInfo as any)?.sourceSystemCode;
      // 判断是否为所内用户
      // 所内用户：dataAuth === '1'
      // 所外用户：dataAuth === '0' || dataAuth === '1'
      return sourceSystemCode === '01' || sourceSystemCode === '02'
        ? dataAuth === '1'
        : dataAuth === '0' || dataAuth === '1';
    };

    return () => (
      <>
        {isShowLockIcons(props.item) && (
          <>
            {props.item?.isVisible !== undefined &&
            +props.item.isVisible === 1 ? (
              <LockOffIcon class="lock-off-icon" />
            ) : (
              <LockOnIcon class="lock-on-icon" />
            )}
          </>
        )}
      </>
    );
  },
});
