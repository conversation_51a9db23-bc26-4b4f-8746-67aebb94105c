<template>
  <div class="factory-data-layout">
    <!-- 返回按钮头部 -->
    <div class="header-container">
      <div class="header-content">
        <button class="header-back-btn" @click="goBack">
          <ChevronLeftIcon class="back-icon" />
          <span>返回</span>
        </button>
        <h1 class="page-title">工厂数据</h1>
        <div class="header-actions">
          <TButton variant="outline" @click="refreshData" :loading="dataLoading">
            刷新数据
          </TButton>
        </div>
      </div>
    </div>

    <!-- 主体三栏布局 -->
    <div class="main-area">
      <!-- 左侧锚点导航 -->
      <div class="anchor-menu">
        <div
          v-for="(item, idx) in menuItems"
          :key="idx"
          :class="['menu-item', { active: activeIndex === idx }]"
          @click="scrollToAnchor(idx)"
        >
          {{ item.title }}
        </div>
      </div>

      <!-- 中间内容区 -->
      <div class="content-area">
        <!-- 每日估价 -->
        <div
          class="content-block"
          ref="dailyPriceRef"
          :data-index="0"
        >
          <div class="content-title">每日估价</div>
          <div class="content-body">
            <!-- 数据统计卡片 -->
            <div class="stats-cards">
              <div 
                v-for="item in getDailyPriceData()" 
                :key="item.id" 
                class="daily-price-card"
              >
                <div class="card-header">
                  <div class="category-badge">{{ item.categoryTwo }}</div>
                  <div class="price-change" :class="getRiseAndFallClass(item.riseAndFall)">
                    <span class="change-icon">{{ getRiseAndFallIcon(item.riseAndFall) }}</span>
                    <span class="change-value">{{ formatRiseAndFall(item.riseAndFall) }}</span>
                  </div>
                </div>
                <div class="card-body">
                  <div class="price-section">
                    <div class="price-value">{{ item.price }}</div>
                    <div class="price-unit">{{ item.dataUnit }}</div>
                  </div>
                  <div class="product-name">{{ item.name }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 金联创估价 -->
        <div
          class="content-block"
          ref="jlcPriceRef"
          :data-index="1"
        >
          <div class="content-title">工厂估价</div>
          <div class="content-body">
            <!-- 主分类选择 -->
            <div class="category-tabs">
              <div
                v-for="category in mainCategories"
                :key="category.key"
                :class="['category-tab', { active: selectedCategory === category.key }]"
                @click="selectCategory(category.key)"
              >
                <div class="category-name">{{ category.label }}</div>
                <div class="sub-categories">
                  <span
                    v-for="(sub, index) in category.subCategories.slice(0, 6)"
                    :key="sub"
                    class="sub-category"
                  >
                    {{ sub }}{{ index < Math.min(category.subCategories.length - 1, 5) ? '、' : '' }}
                  </span>
                  <span v-if="category.subCategories.length > 6" class="more-indicator">...</span>
                </div>
              </div>
            </div>

            <!-- 子分类选择 -->
            <div class="sub-category-tabs" v-if="currentSubCategories.length > 0">
              <TButton
                v-for="subCategory in currentSubCategories"
                :key="subCategory"
                :variant="selectedSubCategory === subCategory ? 'base' : 'text'"
                size="small"
                @click="selectSubCategory(subCategory)"
              >
                {{ subCategory }}
              </TButton>
            </div>

            <!-- 图表展示 -->
            <div class="chart-section">
              <div class="chart-header">
                <h3>{{ getJlcChartTitle() }}</h3>
              </div>
              <div class="chart-content">
                <EnhancedLineChart
                  v-if="getCurrentJlcChartData().length > 0"
                  :data="getCurrentJlcChartData()"
                  title=""
                  :height="400"
                  x-axis-key="dataDate"
                  y-axis-key="price"
                  series-key="name"
                  :show-legend="true"
                  :smooth="true"
                />
                <div v-else class="chart-loading">
                  <TLoading size="large" text="正在加载数据..." />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 金联创行业指数 -->
        <div
          class="content-block"
          ref="industryIndexRef"
          :data-index="2"
        >
          <div class="content-title">工厂行业指数</div>
          <div class="content-body">
            <!-- 行业指数分类选择 -->
            <div class="industry-category-tabs">
              <TButton
                v-for="category in industryCategories"
                :key="category.key"
                :variant="selectedIndustryCategory === category.key ? 'base' : 'text'"
                size="medium"
                @click="selectIndustryCategory(category.key)"
              >
                {{ category.label }}
              </TButton>
            </div>

            <!-- 行业指数图表 -->
            <div class="chart-section">
              <div class="chart-header">
                <h3>{{ getIndustryChartTitle() }}</h3>
              </div>
              <div class="chart-content">
                <EnhancedLineChart
                  v-if="getCurrentIndustryChartData().length > 0"
                  :data="getCurrentIndustryChartData()"
                  title=""
                  :height="400"
                  x-axis-key="dataDate"
                  y-axis-key="price"
                  series-key="name"
                  :show-legend="true"
                  :smooth="true"
                />
                <div v-else class="chart-loading">
                  <TLoading size="large" text="正在加载数据..." />
                </div>
              </div>
            </div>

            <!-- 数据表格 -->
            <div class="data-table-section">
              <div class="table-header">
                <h3>最新数据</h3>
              </div>
              <TTable
                :data="getIndustryTableData()"
                :columns="industryTableColumns"
                :pagination="false"
                size="small"
                stripe
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { 
  Button as TButton, 
  Loading as TLoading, 
  Table as TTable,
  MessagePlugin
} from 'tdesign-vue-next'
import { ChevronLeftIcon } from 'tdesign-icons-vue-next'
import { useRouter } from 'vue-router'
import EnhancedLineChart from './EnhancedLineChart.vue'
import resData from './res.json'
import { 
  getCategoryInfo,
  queryFactoryData,
  type FactoryDataItem
} from './api'

// 路由
const router = useRouter()

// 返回首页方法
const goBack = () => {
  router.push('/')
}

// 锚点导航相关
const activeIndex = ref(0)
const menuItems = ref([
  { title: '每日估价' },
  { title: '工厂估价' },
  { title: '工厂行业指数' }
])

// 产品分类 - 基于 res.json 数据结构
const selectedCategory = ref('化工')
const selectedSubCategory = ref('LLDPE')

// 行业指数分类
const selectedIndustryCategory = ref('化工')

// 主分类数据
const mainCategories = ref([
  {
    key: '石油',
    label: '石油',
    subCategories: ['原油', '汽油', '柴油', '保税船燃', '液化气']
  },
  {
    key: '天然气',
    label: '天然气',
    subCategories: ['LNG']
  },
  {
    key: '化工',
    label: '化工',
    subCategories: ['苯乙烯', '甲醇', 'PTA', 'LLDPE', 'PP', '天然橡胶', '尿素']
  },
  {
    key: '钢铁',
    label: '钢铁',
    subCategories: ['螺纹钢', '热轧板卷']
  }
])


// 行业指数分类数据
const industryCategories = ref([
  { key: '化工', label: '化工' },
  { key: '石油', label: '石油' }
])

// 计算当前选中分类的子分类
const currentSubCategories = computed(() => {
  const category = mainCategories.value.find(cat => cat.key === selectedCategory.value)
  return category ? category.subCategories : []
})

// 原始 JSON 数据（从 res.json 文件导入）
const jlcPriceData = ref([])
const dailyPriceData = ref([])
const industryIndexData = ref<FactoryDataItem[]>([])

// 响应式数据
const dataLoading = ref(false)
const errorMessage = ref('')
const lastUpdateTime = ref('')

// 行业指数表格列定义
const industryTableColumns = [
  { colKey: 'date', title: '日期', width: 120 },
  { colKey: 'product', title: '产品', width: 200 },
  { colKey: 'price', title: '价格', width: 100 },
  { colKey: 'change', title: '涨跌幅', width: 100 },
  { colKey: 'riseAndFall', title: '涨跌', width: 100 },
]

// 锚点导航方法
const scrollToAnchor = (index: number) => {
  const blocks = document.querySelectorAll('.content-block')
  
  if (blocks && blocks[index]) {
    const targetBlock = blocks[index] as HTMLElement
    const offsetTop = targetBlock.offsetTop
    const headerHeight = 60 // 头部高度
    const extraOffset = 10 // 额外偏移，避免标题被遮挡
    
    window.scrollTo({
      top: offsetTop - headerHeight - extraOffset,
      behavior: 'smooth'
    })
  }
  
  activeIndex.value = index
}

const onScroll = () => {
  const blocks = document.querySelectorAll('.content-block')
  
  if (!blocks) return
  
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const headerHeight = 80
  const extraOffset = 40
  let currentIndex = 0
  
  blocks.forEach((block, index) => {
    const element = block as HTMLElement
    const offsetTop = element.offsetTop - headerHeight - extraOffset
    
    if (scrollTop >= offsetTop) {
      currentIndex = index
    }
  })
  
  activeIndex.value = currentIndex
}

// 分类选择方法
const selectCategory = (categoryKey: string) => {
  selectedCategory.value = categoryKey
  // 自动选择该分类下的第一个子分类
  const category = mainCategories.value.find(cat => cat.key === categoryKey)
  if (category && category.subCategories.length > 0) {
    selectedSubCategory.value = category.subCategories[0]
  }
}

const selectSubCategory = (subCategoryKey: string) => {
  selectedSubCategory.value = subCategoryKey
}

// 获取当前金联创图表数据
const getCurrentJlcChartData = () => {
  if (!jlcPriceData.value.length) return []
  
  // 过滤出当前选中分类和子分类的数据
  const filteredData = jlcPriceData.value.filter(item => 
    item.categoryOne === selectedCategory.value && 
    item.categoryTwo === selectedSubCategory.value
  )
  
  // 转换数据格式以适配图表组件
  return filteredData.map(item => ({
    dataDate: item.dataDate,
    price: parseFloat(item.price),
    name: item.name,
    dataUnit: item.dataUnit
  }))
}

// 获取金联创图表标题
const getJlcChartTitle = () => {
  return `${selectedCategory.value} - ${selectedSubCategory.value}价格走势图`
}

// 每日估价相关方法
const getDailyPriceData = () => {
  // 取前6条数据
  return dailyPriceData.value.slice(0, 6)
}

const formatRiseAndFall = (riseAndFall: string | number) => {
  if (!riseAndFall || riseAndFall === '0.00' || riseAndFall === 0) return '0'
  const value = parseFloat(riseAndFall.toString())
  return value > 0 ? `+${value}` : value.toString()
}

const getRiseAndFallClass = (riseAndFall: string | number) => {
  if (!riseAndFall || riseAndFall === '0.00' || riseAndFall === 0) return 'neutral'
  const value = parseFloat(riseAndFall.toString())
  return value > 0 ? 'negative' : 'positive'
}

const getRiseAndFallIcon = (riseAndFall: string | number) => {
  if (!riseAndFall || riseAndFall === '0.00' || riseAndFall === 0) return ''
  const value = parseFloat(riseAndFall.toString())
  return value > 0 ? '↑' : '↓'
}

// 行业指数分类选择方法
const selectIndustryCategory = (categoryKey: string) => {
  selectedIndustryCategory.value = categoryKey
}

// 获取当前行业指数图表数据
const getCurrentIndustryChartData = () => {
  if (!industryIndexData.value.length) return []
  
  // 过滤出当前选中分类的数据
  const filteredData = industryIndexData.value.filter(item => 
    item.categoryOne === selectedIndustryCategory.value
  )
  
  // 转换数据格式以适配图表组件
  return filteredData.map(item => ({
    dataDate: item.dataDate,
    price: parseFloat(item.price),
    name: item.name
  }))
}

// 获取行业指数图表标题
const getIndustryChartTitle = () => {
  return `工厂${selectedIndustryCategory.value}行业指数`
}

// 获取行业指数表格数据
const getIndustryTableData = () => {
  if (!industryIndexData.value.length) return []
  
  // 过滤出当前选中分类的最新数据
  const filteredData = industryIndexData.value.filter(item => 
    item.categoryOne === selectedIndustryCategory.value
  )
  
  // 按日期排序，取最新的几条数据
  const sortedData = filteredData.sort((a, b) => new Date(b.dataDate) - new Date(a.dataDate))
  
  // 转换数据格式以适配表格组件
  return sortedData.slice(0, 10).map(item => ({
    date: item.dataDate,
    product: item.name,
    price: item.price,
    change: item.chg || '-',
    riseAndFall: item.riseAndFall || '-'
  }))
}

// 加载模拟数据
const loadMockData = () => {
  // 使用 res.json 文件的真实数据
  if (resData && resData.result) {
    // 提取 jlcPrice 数组中的所有数据项
    if (resData.result.jlcPrice) {
      jlcPriceData.value = []
      resData.result.jlcPrice.forEach(category => {
        if (category.data && Array.isArray(category.data)) {
          jlcPriceData.value.push(...category.data)
        }
      })
      console.log('Loaded JLC Price Data:', jlcPriceData.value.length, 'items')
    }
    
    // 提取 industryIndex 数组中的所有数据项
    if (resData.result.industryIndex) {
      industryIndexData.value = resData.result.industryIndex
      console.log('Loaded Industry Index Data:', industryIndexData.value.length, 'items')
    }
    
    // 提取 dailyPrice 数组中的所有数据项
    if (resData.result.dailyPrice) {
      dailyPriceData.value = resData.result.dailyPrice
      console.log('Loaded Daily Price Data:', dailyPriceData.value.length, 'items')
    }
  }

  lastUpdateTime.value = new Date().toLocaleString()
}

// 加载所有数据
const loadAllData = async () => {
  dataLoading.value = true
  errorMessage.value = ''
  
  try {
    const data = await queryFactoryData()
    if (data) {
      // chartData.value = data
      jlcPriceData.value = []
      data.jlcPrice.forEach(category => {
        if (category.data && Array.isArray(category.data)) {
          jlcPriceData.value.push(...category.data)
        }
      })
      // 提取 industryIndex 数组中的所有数据项
      if (data.industryIndex) {
        industryIndexData.value = data.industryIndex
      }
      // 提取 dailyPrice 数组中的所有数据项
      if (data.dailyPrice) {
        dailyPriceData.value = data.dailyPrice
      }
    } else {
      // 如果没有真实数据，使用模拟数据
      loadMockData()
    }
    
    lastUpdateTime.value = new Date().toLocaleString()
    MessagePlugin.success('数据加载完成')
  } catch (error) {
    console.error('Load data failed:', error)
    errorMessage.value = error.message || '获取数据失败'
    MessagePlugin.warning('获取真实数据失败，使用模拟数据')
    loadMockData()
  } finally {
    dataLoading.value = false
  }
}

// 加载数据类型
const loadCategoryInfo = async () => {
  dataLoading.value = true
  errorMessage.value = ''
  
  try {
    const data = await getCategoryInfo()
    if (data) {
      mainCategories.value = data
    }
  } catch (error) {
    console.error('Load data failed:', error)
  } finally {
    dataLoading.value = false
  }
}

// 刷新数据
const refreshData = async () => {
  await loadCategoryInfo()
  await loadAllData()
}

// 生命周期
onMounted(async () => {
  await loadCategoryInfo()
  await loadAllData()
  // 添加滚动事件监听
  window.addEventListener('scroll', onScroll)
})

onUnmounted(() => {
  // 移除滚动事件监听
  window.removeEventListener('scroll', onScroll)
})
</script>
<style scoped lang="scss">
.factory-data-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;

  .header-container {
    background: white;
    padding: 16px 24px;
    border-bottom: 1px solid #e7e7e7;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    
    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      
      .header-back-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        background: none;
        border: none;
        color: #6b7280;
        cursor: pointer;
        font-size: 15px;
        padding: 8px;
        border-radius: 4px;
        transition: all 0.2s ease;
        
        &:hover {
          color: #2563eb;
          background-color: #f3f4f6;
        }
        
        .back-icon {
          font-size: 16px;
        }
      }
      
      .page-title {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
      }
      
      .header-actions {
        display: flex;
        gap: 12px;
      }
    }
  }

  .main-area {
    display: flex;
    flex: 1;

    .anchor-menu {
      width: 200px;
      background: white;
      border-right: 1px solid #e7e7e7;
      padding: 0;
      position: sticky;
      top: 0;
      height: fit-content;
      max-height: calc(100vh - 80px);
      overflow-y: auto;

      .menu-item {
        padding: 16px 24px;
        cursor: pointer;
        color: #6b7280;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s ease;
        position: relative;
        width: 100%;
        display: block;

        &:hover {
          background-color: #f9fafb;
          color: #374151;
        }

        &.active {
          background-color: #eff6ff;
          color: #2563eb;
          font-weight: 600;
          
          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background-color: #2563eb;
          }
        }
      }
    }

    .content-area {
      flex: 1;
      padding: 24px;
      padding-bottom: 80px; /* 添加底部内边距防止页脚遮挡 */

      .content-block {
        margin-bottom: 40px;

        .content-title {
          font-size: 20px;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 20px;
          padding-bottom: 12px;
          border-bottom: 2px solid #e5e7eb;
        }

        .content-body {
          .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 16px;
            margin-bottom: 24px;

            .daily-price-card {
              background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
              border-radius: 16px;
              padding: 0;
              box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
              border: 1px solid #e2e8f0;
              transition: all 0.3s ease;
              overflow: hidden;
              position: relative;

              &:hover {
                box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
                transform: translateY(-4px);
                border-color: #3b82f6;
              }

              &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg, #3b82f6, #1d4ed8);
              }

              .card-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 16px 20px 12px;
                border-bottom: 1px solid #f1f5f9;

                .category-badge {
                  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
                  color: white;
                  padding: 6px 12px;
                  border-radius: 20px;
                  font-size: 12px;
                  font-weight: 600;
                  letter-spacing: 0.5px;
                  text-transform: uppercase;
                  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
                }

                .price-change {
                  display: flex;
                  align-items: center;
                  gap: 4px;
                  padding: 4px 8px;
                  border-radius: 12px;
                  font-size: 12px;
                  font-weight: 700;
                  min-width: 60px;
                  justify-content: center;

                  &.positive {
                    background: rgba(16, 185, 129, 0.1);
                    color: #059669;
                    border: 1px solid rgba(16, 185, 129, 0.2);
                  }

                  &.negative {
                    background: rgba(239, 68, 68, 0.1);
                    color: #dc2626;
                    border: 1px solid rgba(239, 68, 68, 0.2);
                  }

                  &.neutral {
                    background: rgba(107, 114, 128, 0.1);
                    color: #6b7280;
                    border: 1px solid rgba(107, 114, 128, 0.2);
                  }

                  .change-icon {
                    font-size: 12px;
                    font-weight: bold;
                  }

                  .change-value {
                    font-size: 11px;
                  }
                }
              }

              .card-body {
                padding: 16px 20px 20px;

                .price-section {
                  display: flex;
                  align-items: baseline;
                  gap: 8px;
                  margin-bottom: 12px;

                  .price-value {
                    font-size: 28px;
                    font-weight: 800;
                    color: #1f2937;
                    line-height: 1;
                    background: linear-gradient(135deg, #1f2937, #374151);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                  }

                  .price-unit {
                    font-size: 14px;
                    color: #6b7280;
                    font-weight: 500;
                  }
                }

                .product-name {
                  font-size: 14px;
                  color: #4b5563;
                  font-weight: 500;
                  line-height: 1.4;
                  background: #f8fafc;
                  padding: 8px 12px;
                  border-radius: 8px;
                  border-left: 3px solid #3b82f6;
                }
              }
            }
          }

          .product-tabs {
            display: flex;
            gap: 8px;
            margin-bottom: 20px;
            padding: 16px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          }

          .category-tabs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
            padding: 16px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

            .category-tab {
              background: #f8fafc;
              border: 2px solid #e2e8f0;
              border-radius: 8px;
              padding: 16px;
              cursor: pointer;
              transition: all 0.2s ease;
              text-align: center;

              &:hover {
                border-color: #3b82f6;
                background: #eff6ff;
              }

              &.active {
                border-color: #2563eb;
                background: #dbeafe;
                box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
              }

              .category-name {
                font-size: 18px;
                font-weight: 600;
                color: #1f2937;
                margin-bottom: 8px;
              }

              .sub-categories {
                font-size: 12px;
                color: #6b7280;
                line-height: 1.4;

                .sub-category {
                  display: inline;
                }

                .more-indicator {
                  color: #9ca3af;
                  font-weight: 500;
                }
              }
            }
          }

          .sub-category-tabs {
            display: flex;
            gap: 8px;
            margin-bottom: 20px;
            padding: 16px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            flex-wrap: wrap;
          }

          .industry-category-tabs {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
            padding: 16px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            flex-wrap: wrap;
          }

          .chart-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;

            .chart-header {
              padding: 16px 20px;
              border-bottom: 1px solid #e5e7eb;

              h3 {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
                color: #1f2937;
              }
            }

            .chart-content {
              padding: 20px;

              .chart-loading {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 400px;
              }
            }
          }

          .data-table-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

            .table-header {
              padding: 16px 20px;
              border-bottom: 1px solid #e5e7eb;

              h3 {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
                color: #1f2937;
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .factory-data-layout {
    .main-area {
      .anchor-menu {
        width: 160px;
      }
    }
  }
}

@media (max-width: 768px) {
  .factory-data-layout {
    .header-container {
      padding: 12px 16px;
      
      .header-content {
        .header-back-btn {
          font-size: 12px;
          padding: 6px;
        }
        
        .page-title {
          font-size: 18px;
        }
        
        .header-actions {
          gap: 8px;
        }
      }
    }
    
    .main-area {
      flex-direction: column;

      .anchor-menu {
        width: 100%;
        display: flex;
        overflow-x: auto;
        padding: 12px 0;

        .menu-item {
          white-space: nowrap;
          min-width: 120px;
          text-align: center;
          border-left: none;
          border-bottom: 3px solid transparent;

          &.active {
            border-left: none;
            border-bottom-color: #2563eb;
          }
        }
      }

      .content-area {
        padding: 16px;

        .content-block {
          .content-body {
            .stats-cards {
              grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
              gap: 12px;

              .stat-card {
                padding: 16px;
              }
            }

            .category-tabs {
              grid-template-columns: 1fr;
              gap: 12px;
              padding: 12px;

              .category-tab {
                padding: 12px;

                .category-name {
                  font-size: 16px;
                }

                .sub-categories {
                  font-size: 11px;
                }
              }
            }

            .sub-category-tabs {
              padding: 12px;
              gap: 6px;
            }

            .product-tabs {
              padding: 12px;
              gap: 6px;
            }
          }
        }
      }
    }
  }
}
</style>
