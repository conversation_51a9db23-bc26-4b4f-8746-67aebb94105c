.container {
  flex: 1;
  display: flex;
  align-items: center;
  width: 100%;
  background: #ffffff;
  box-shadow: 0px 4px 4px 0px rgba(255, 255, 255, 0.25);
  border-radius: 0;
  border: 2px solid #0f569f;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  padding: 12px 30px;
  height: auto;

  @media screen and (max-width: 768px) {
    flex-direction: column;
    gap: 15px;
  }
}

.input {
  flex-grow: 1;
  width: 100%;
  border: none;
  background-color: transparent;
  font-size: 16px;

  &:focus {
    outline: 0;
  }

  &::placeholder {
    font-weight: 400;
    font-size: 16px;
    color: #999999;
  }
}

.buttons {
  display: flex;
  align-items: center;
  gap: 10px;

  @media screen and (max-width: 480px) {
    flex-direction: column;
    gap: 10px;
  }
}

.button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  border: none;
  cursor: pointer;
  padding: 18px 0;
  font-size: 16px;
  font-weight: 500;
  color: #c0c4cc;
}
.qaButton {
  background-color: #c4e3ff;
  font-weight: 500;
  font-size: 16px;
  color: #0f569f;
}

.searchButton {
  background-color: #0f569f;
  font-weight: 500;
  font-size: 16px;
  color: #ffffff;
}

.button img {
  max-width: 100%;
  max-height: 100%;
  margin-right: 8px;
}
