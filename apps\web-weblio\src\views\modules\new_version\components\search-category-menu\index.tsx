import type { TreeNodeValue } from 'tdesign-vue-next';
import type { PropType } from 'vue';

import { Loading, Tree } from 'tdesign-vue-next';
import { computed, defineComponent, ref, watch } from 'vue';

import styles from './style.module.less';

export default defineComponent({
  name: 'SearchCategoryMenu',
  props: {
    data: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    checked: {
      type: Array as PropType<TreeNodeValue[]>,
      default: () => [],
    },
    expanded: {
      type: Array as PropType<TreeNodeValue[]>,
      default: () => [],
    },
  },
  emits: ['update:checked', 'update:expanded', 'change'],
  setup(props, { emit }) {
    const treeFilter = ref('');

    const checkedKeys = ref<TreeNodeValue[]>(props.checked);
    const expandedKeys = ref<TreeNodeValue[]>(props.expanded);

    watch(
      () => props.checked,
      (val) => {
        checkedKeys.value = val;
      },
    );

    watch(
      () => props.expanded,
      (val) => {
        expandedKeys.value = val;
      },
    );

    const handleCheckedChange = (val: TreeNodeValue[]) => {
      checkedKeys.value = val;
      emit('update:checked', val);
      emit('change', val);
    };

    const handleExpandedChange = (val: TreeNodeValue[]) => {
      expandedKeys.value = val;
      emit('update:expanded', val);
    };

    // 递归过滤树节点
    function filterTree(data: any[], keyword: string) {
      if (!keyword) return data;
      return data
        .map((node) => {
          const children = node.children
            ? filterTree(node.children, keyword)
            : [];
          if (
            (node.classifyName && node.classifyName.includes(keyword)) ||
            (children && children.length > 0)
          ) {
            return { ...node, children };
          }
          return null;
        })
        .filter(Boolean);
    }

    const filteredTreeData = computed(() =>
      filterTree(props.data || [], treeFilter.value.trim()),
    );

    return () => (
      <div class={styles['search-category-menu']}>
        <input
          class={styles['tree-filter-input']}
          placeholder="检索分类/节点"
          v-model={treeFilter.value}
        />
        {props.loading ? (
          <Loading style={{ marginTop: '32px' }} text="加载中..." />
        ) : (
          <Tree
            checkable
            data={filteredTreeData.value}
            expanded={expandedKeys.value}
            keys={{
              label: 'classifyName',
              value: 'classifyCode',
            }}
            modelValue={checkedKeys.value}
            onChange={handleCheckedChange}
            onExpand={handleExpandedChange}
          />
        )}
      </div>
    );
  },
});
