.card-item {
  display: flex;
  position: relative;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
  cursor: pointer;
  padding: 15px;
  margin-bottom: 20px;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
  }

  .card-img {
    width: 200px;
    height: 120px;
    overflow: hidden;
    margin-right: 20px;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .card-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
  }

  .card-desc {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
    flex-grow: 1;
  }

  .card-meta {
    font-size: 12px;
    color: #999;
    display: flex;
    gap: 20px;

    span {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .card-actions {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    gap: 10px;
  }

  .card-tags-overlay {
    position: absolute;
    top: 15px;
    left: 15px;
  }

  .card-type-tag {
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: #fff;
  }
}
