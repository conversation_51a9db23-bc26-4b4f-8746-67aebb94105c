<script setup lang="ts">
import type { TabOption } from '../../store/tabs';

import { ref } from 'vue';

interface Props {
  items: TabOption[];
}

const props = defineProps<Props>();
const emit = defineEmits(['item-click']);

const activeItem = ref<null | TabOption>(null);

const handleItemClick = (item: TabOption) => {
  emit('item-click', item);
};

const handleMouseEnter = (item: TabOption) => {
  if (item.children) {
    activeItem.value = item;
  }
};

const handleMouseLeave = () => {
  activeItem.value = null;
};
</script>

<template>
  <div class="submenu" @mouseleave="handleMouseLeave">
    <div
      v-for="item in items"
      :key="item.key"
      class="submenu-item"
      @click.stop="handleItemClick(item)"
      @mouseenter="handleMouseEnter(item)"
    >
      {{ item.label }}
      <div
        v-if="item.children && activeItem?.key === item.key"
        class="submenu-container"
      >
        <Submenu :items="item.children" @item-click="handleItemClick" />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.submenu {
  position: absolute;
  top: 0;
  left: 100%;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
}

.submenu-item {
  padding: 8px 16px;
  cursor: pointer;
  white-space: nowrap;
  position: relative;

  &:hover {
    background-color: #f3f4f6;
  }
}

.submenu-container {
  position: absolute;
  top: 0;
  left: 100%;
}
</style>
