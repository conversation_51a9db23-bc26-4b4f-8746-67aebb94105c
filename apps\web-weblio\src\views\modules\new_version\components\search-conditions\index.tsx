import type { PropType } from 'vue';

import {
  Checkbox,
  CheckboxGroup,
  Radio,
  RadioGroup,
  Tag,
} from 'tdesign-vue-next';
import { defineComponent } from 'vue';

import styles from './style.module.less';

interface Option {
  value: number | string;
  label: string;
  theme?: string;
}

export default defineComponent({
  name: 'SearchConditions',
  props: {
    selectedShareTypes: {
      type: Array as PropType<(number | string)[]>,
      default: () => [],
    },
    shareTypeOptions: {
      type: Array as PropType<Option[]>,
      default: () => [],
    },
    selectedYears: {
      type: Array as PropType<(number | string)[]>,
      default: () => [],
    },
    yearOptions: {
      type: Array as PropType<Option[]>,
      default: () => [],
    },
    selectedDocumentTypes: {
      type: [String, Number] as PropType<number | string>,
      default: '',
    },
    documentTypeOptions: {
      type: Array as PropType<Option[]>,
      default: () => [],
    },
    isYearDisabled: {
      type: Boolean,
      default: false,
    },
    keywordOptions: {
      type: Array as PropType<Option[]>,
      default: () => [],
    },
    selectedKeywords: {
      type: Array as PropType<(number | string)[]>,
      default: () => [],
    },
  },
  emits: [
    'update:selectedShareTypes',
    'update:selectedYears',
    'update:selectedKeywords',
    'update:selectedDocumentTypes',
    'shareTypeChange',
    'search',
  ],
  setup(props, { emit }) {
    const handleShareTypeChange = (values: (number | string)[]) => {
      emit('update:selectedShareTypes', values);
      emit('shareTypeChange', values);
    };

    const handleDocumentTypeChange = (value: number | string) => {
      emit('update:selectedDocumentTypes', value);
      emit('search');
    };

    const handleYearChange = (values: (number | string)[]) => {
      emit('update:selectedYears', values);
      emit('search');
    };

    const handleKeywordClick = (value: number | string) => {
      const updatedKeywords = [...props.selectedKeywords];
      const index = updatedKeywords.indexOf(value);
      if (index === -1) {
        updatedKeywords.push(value);
      } else {
        updatedKeywords.splice(index, 1);
      }
      emit('update:selectedKeywords', updatedKeywords);
    };

    type TagTheme = 'danger' | 'default' | 'primary' | 'success' | 'warning';

    const getValidTheme = (theme?: string): TagTheme => {
      const validThemes: TagTheme[] = [
        'default',
        'primary',
        'warning',
        'danger',
        'success',
      ];
      if (theme && (validThemes as string[]).includes(theme)) {
        return theme as TagTheme;
      }
      return 'default';
    };

    return () => (
      <div class={styles.container}>
        <div class="condition-bar">
          {/* 共享方式 */}
          <div class="condition-row">
            <div class="condition-label">共享方式</div>
            <CheckboxGroup
              class="condition-group"
              onChange={handleShareTypeChange}
              value={props.selectedShareTypes}
            >
              {props.shareTypeOptions.map((item) => (
                <Checkbox key={item.value} value={item.value}>
                  {item.label}
                </Checkbox>
              ))}
            </CheckboxGroup>
          </div>
          {/* 资源类型 */}
          <div class="condition-row">
            <div class="condition-label">资源类型</div>
            <RadioGroup
              class="condition-group"
              onChange={handleDocumentTypeChange}
              value={props.selectedDocumentTypes}
            >
              {props.documentTypeOptions.map((item) => (
                <Radio key={item.value} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </RadioGroup>
          </div>

          {/* 年份 */}
          <div class="condition-row">
            <div class="condition-label">年份</div>
            <CheckboxGroup
              class="condition-group"
              disabled={props.isYearDisabled}
              onChange={handleYearChange}
              value={props.selectedYears}
            >
              {props.yearOptions.map((item) => (
                <Checkbox key={item.value} value={item.value}>
                  {item.label}
                </Checkbox>
              ))}
            </CheckboxGroup>
          </div>

          {/* 关键词 */}
          {props.keywordOptions.length > 0 && (
            <div class="condition-row">
              <div class="condition-label">关键词</div>
              <div class="keyword-tags">
                {props.keywordOptions.map((tag) => (
                  <Tag
                    class={{
                      'tag-selected': props.selectedKeywords.includes(
                        tag.value,
                      ),
                    }}
                    key={tag.value}
                    onClick={() => handleKeywordClick(tag.value)}
                    theme={getValidTheme(tag.theme)}
                  >
                    {tag.label}
                  </Tag>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  },
});
