import type { PropType } from 'vue';

import type { HomeListSection } from './index';

import { defineComponent } from 'vue';

import styles from './style.module.less';

export default defineComponent({
  name: 'HomeList',
  props: {
    sections: {
      type: Array as PropType<HomeListSection[]>,
      required: true,
    },
  },
  setup(props) {
    const renderSection = (section: HomeListSection) => {
      if (section.type === 'single') {
        return <div class={styles.section}>{section.content()}</div>;
      }

      if (section.type === 'row') {
        return (
          <div class={styles.section}>
            <div class={styles['category-tools-row']}>
              {section.children.map((child) => (
                <div class={styles[child.class]}>{child.content()}</div>
              ))}
            </div>
          </div>
        );
      }

      return null;
    };

    return () => (
      <div class={styles['home-list-container']}>
        {props.sections.map((section) => renderSection(section))}
      </div>
    );
  },
});
