<template>
  <div class="page-header">
    <h1 class="page-title">Method</h1>
    <button class="back-button" @click="goBack">
      返回
    </button>
  </div>

  <div class="method-codes-page">
    <div class="content-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p class="loading-text">正在加载信息...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <p class="error-text">{{ error }}</p>
        <button class="retry-button" @click="fetchMethodData">重试</button>
      </div>

      <!-- 数据显示 -->
      <div v-else>
        <!-- 循环显示API返回的方法组数据 -->
        <div v-for="(methodGroup, groupIndex) in methodList" :key="groupIndex" class="method-group">
          <!-- 方法标题（只显示一次） -->
          <div class="method-title" v-html="methodGroup[0]?.methodTitle || '未知方法'"></div>
          
          <!-- 循环显示该方法组下的所有方法项 -->
          <div v-for="(method, methodIndex) in methodGroup" :key="methodIndex" class="method-item">
            <div class="content-style">
              <div class="symbol-text" v-html="method.methodSymbol || ''"></div>
              <div class="description-text" v-html="method.methodDescribe || '暂无描述'"></div>
            </div>
          </div>
        </div>

        <!-- 如果没有数据 -->
        <div v-if="methodList.length === 0" class="no-data-container">
          <p class="no-data-text">暂无信息</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRouter ,useRoute} from 'vue-router';
import { getMethodData } from '../api';

// 定义方法数据的类型接口
interface MethodItem {
  methodTitle: string;
  methodDescribe: string;
  methodSymbol: string;
}

// 定义方法组的类型（二维数组）
type MethodGroup = MethodItem[];
type MethodList = MethodGroup[];

const router = useRouter();
const route = useRoute();
// 响应式数据，添加明确的类型定义
const methodList = ref<MethodList>([]);
const loading = ref<boolean>(false);
const error = ref<string>('');

// 获取数据
const fetchMethodData = async (): Promise<void> => {
  try {
    loading.value = true;
    error.value = '';

    // 调用API获取数据
    const response = await getMethodData();

    // 根据API返回的数据结构调整
    if (response && Array.isArray(response)) {
      methodList.value = response;
    } else {
      methodList.value = [];
    }
  } catch (err: unknown) {
    console.error('获取数据失败:', err);
    error.value = '获取数据失败，请稍后重试';
  } finally {
    loading.value = false;
  }
};

// 返回上一页
const goBack = (): void => {
  router.back();
};

// 组件挂载时获取数据
onMounted(() => {
  fetchMethodData();
});
</script>

<style scoped>
.method-codes-page {
  min-height: 100vh;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.page-title {
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: #40a9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.content-container {
  margin: 0 auto;
}

.method-group {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.method-group:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.method-item {
  margin-bottom: 15px;
}

.method-item:last-child {
  margin-bottom: 0;
}

.method-title {
  color: #1890ff;
  font-size: 18px;
  font-weight: 600;
  display: inline-block;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #f0f0f0;
  width: 100%;
}

.content-style {
  display: flex;
  margin-top: 10px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #1890ff;
}

.symbol-text {
  width: 15%;
  font-size: 16px;
  color: #1890ff;
  font-weight: 600;
  min-width: 80px;
}

.description-text {
  width: 85%;
  font-size: 16px;
  line-height: 1.6;
  color: #333;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: #666;
  font-size: 16px;
  margin: 0;
}

/* 错误状态样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 2px solid #ff4d4f;
}

.error-text {
  color: #ff4d4f;
  font-size: 16px;
  margin: 0 0 20px 0;
  text-align: center;
}

.retry-button {
  padding: 10px 20px;
  background: #ff4d4f;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.retry-button:hover {
  background: #ff7875;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
}

/* 无数据状态样式 */
.no-data-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.no-data-text {
  color: #999;
  font-size: 16px;
  margin: 0;
}

.additional-info {
  background: linear-gradient(135deg, #1e5fae 0%, #2a69c4 100%);
  color: white;
  border-radius: 12px;
  padding: 30px;
  margin-top: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.info-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 15px 0;
  color: white;
}

.info-text {
  font-size: 16px;
  line-height: 1.7;
  margin: 0;
  opacity: 0.95;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .method-codes-page {
    padding: 15px;
  }

  .page-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .page-title {
    font-size: 20px;
  }

  .method-group {
    padding: 20px;
  }

  .method-title {
    font-size: 16px;
  }

  .content-style {
    flex-direction: column;
    padding: 12px;
  }

  .symbol-text {
    width: 100%;
    margin-bottom: 8px;
    font-size: 14px;
    min-width: auto;
  }

  .description-text {
    width: 100%;
    font-size: 14px;
  }

  .additional-info {
    padding: 20px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.method-group {
  animation: fadeInUp 0.6s ease-out;
}

.method-group:nth-child(2) {
  animation-delay: 0.1s;
}

.method-group:nth-child(3) {
  animation-delay: 0.2s;
}

.method-item {
  animation: fadeInUp 0.4s ease-out;
}

.method-item:nth-child(2) {
  animation-delay: 0.05s;
}

.method-item:nth-child(3) {
  animation-delay: 0.1s;
}

.additional-info {
  animation: fadeInUp 0.6s ease-out;
  animation-delay: 0.3s;
}
</style>
