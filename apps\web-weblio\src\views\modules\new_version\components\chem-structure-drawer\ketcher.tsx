import { defineComponent, onMounted, onUnmounted, ref, watch } from 'vue';

export default defineComponent({
  name: '<PERSON><PERSON><PERSON>',
  props: {
    modelValue: {
      type: Object,
      required: true,
    },
  },
  emits: ['update:modelValue'],
  setup(props, { emit, expose }) {
    const idKetcher = ref<HTMLIFrameElement | null>(null);
    const localValue = ref({ ...props.modelValue });

    const setMolecule = (e: any) =>
      idKetcher.value?.contentWindow?.ketcher?.setMolecule(e);

    watch(
      () => props.modelValue,
      (val) => {
        localValue.value = { ...val };
        setMolecule(localValue.value.config);
      },
      { deep: true },
    );

    let initTimer: null | number = null;
    let observer: MutationObserver | null = null;

    const updateModelValue = (newVal: any) => {
      emit('update:modelValue', { ...newVal });
    };

    const initKetcher = () => {
      if (initTimer) {
        clearTimeout(initTimer);
      }

      initTimer = window.setTimeout(() => {
        if (idKetcher.value?.contentWindow) {
          idKetcher.value.contentWindow.ketcher?.setMolecule(
            localValue.value.config,
          );
        }

        idKetcher.value?.contentWindow?.addEventListener(
          'wheel',
          (event) => {
            event.preventDefault();
          },
          { passive: false },
        );

        if (idKetcher.value?.contentWindow) {
          if (observer) {
            observer.disconnect();
          }
          observer = new MutationObserver(handleMutation);
          if (idKetcher.value.contentDocument) {
            observer.observe(idKetcher.value.contentDocument, {
              childList: true,
              subtree: true,
            });
          }
        }
        async function handleMutation() {
          if (idKetcher.value?.contentWindow) {
            const config =
              await idKetcher.value.contentWindow.ketcher?.getKet();
            const smiles =
              await idKetcher.value.contentWindow.ketcher?.getSmiles();
            if (config) {
              const img =
                await idKetcher.value.contentWindow.ketcher?.generateImage(
                  config,
                  {
                    outputFormat: 'png',
                    backgroundColor: '255, 255, 255',
                  },
                );
              localValue.value = { config, smiles, img };
              updateModelValue(localValue.value);
            }
          }
        }
      }, 500);
    };

    const getSmiles = () =>
      idKetcher.value?.contentWindow?.ketcher?.getSmiles();

    const getMolfile = () =>
      idKetcher.value?.contentWindow?.ketcher?.getMolfile();

    const getKet = () => idKetcher.value?.contentWindow?.ketcher?.getKet();

    onMounted(() => {
      setMolecule(localValue.value);
    });

    onUnmounted(() => {
      if (initTimer) {
        clearTimeout(initTimer);
        initTimer = null;
      }

      if (observer) {
        observer.disconnect();
        observer = null;
      }
    });

    expose({
      getSmiles,
      getMolfile,
      setMolecule,
      getKet,
    });

    return () => (
      <iframe
        class="frame h-full w-full"
        onLoad={initKetcher}
        ref={idKetcher}
        src="/static/standalone/index.html"
      ></iframe>
    );
  },
});
