.home-list-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.section {
  width: 100%;
  padding: 24px 48px;
  box-sizing: border-box;

  &:nth-child(odd) {
    background-color: #f0f0f3;
  }

  &:nth-child(even) {
    background-color: #ffffff;
  }
}

.category-tools-row {
  display: flex;
  gap: 40px;
  align-items: stretch;
}

.category-col {
  flex: 2;
  min-width: 0;
}

.tools-col {
  flex: 1;
  min-width: 340px;
}

@media (max-width: 900px) {
  .category-tools-row {
    flex-direction: column;
    gap: 24px;
  }
}
