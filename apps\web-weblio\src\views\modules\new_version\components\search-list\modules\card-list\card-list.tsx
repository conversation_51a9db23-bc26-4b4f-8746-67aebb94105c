import type { PropType } from 'vue';

import CardItem from '#/views/modules/new_version/components/search-list/modules/card-item/card-item';
import { defineComponent } from 'vue';

import styles from './card-list.module.less';

export default defineComponent({
  name: 'SearchCardList',
  props: {
    data: {
      type: Array as PropType<any[]>,
      required: true,
    },
  },
  emits: ['itemClick', 'collectionChange'],
  setup(props, { emit }) {
    const onItemClick = (item: any) => {
      emit('itemClick', item);
    };

    const onCollectionChange = (item: any, event: any) => {
      emit('collectionChange', item, event);
    };

    return () => (
      <div class={styles['card-list']}>
        {props.data.map((item) => (
          <CardItem
            item={item}
            key={item.id}
            onCollectionChange={(event: any) => onCollectionChange(item, event)}
            onItemClick={() => onItemClick(item)}
          />
        ))}
      </div>
    );
  },
});
