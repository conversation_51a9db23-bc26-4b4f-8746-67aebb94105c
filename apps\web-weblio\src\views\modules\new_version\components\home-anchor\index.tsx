import { defineComponent, ref } from 'vue';

import styles from './style.module.less';

export default defineComponent({
  name: 'HomeAnchor',
  emits: ['anchor-click'],
  setup(props, { emit }) {
    const anchors = ref([
      {
        label: '石化化工全链条',
        value: 'a',
      },
      {
        label: '资源类型',
        value: 'b',
      },
      {
        label: '数据统计',
        value: 'c',
      },
      {
        label: '数据工具',
        value: 'd',
      },
    ]);
    const active = ref('a');
    const handleClick = (item) => {
      active.value = item.value;
      emit('anchor-click', item.value);
    };
    return () => (
      <div class={[styles.container, 'home-anchor']}>
        {anchors.value.map((item) => (
          <div
            class={[
              styles.item,
              active.value === item.value ? styles.active : '',
            ]}
            onClick={() => handleClick(item)}
          >
            {item.label}
          </div>
        ))}
      </div>
    );
  },
});
