import type { PropType } from 'vue';

import Collection from '#/components/collection/index.vue';
import Share from '#/components/share/index.vue';
import { Table } from 'tdesign-vue-next';
import { defineComponent } from 'vue';

import styles from './table-list.module.less';

export default defineComponent({
  name: 'SearchTableList',
  props: {
    data: {
      type: Array as PropType<any[]>,
      required: true,
    },
    columns: {
      type: Array as PropType<any[]>,
      required: true,
    },
  },
  emits: ['collectionChange'],
  setup(props, { emit }) {
    const onCollectionChange = (row: any, event: any) => {
      emit('collectionChange', row, event);
    };

    return () => (
      <Table
        class={styles['result-table']}
        columns={props.columns}
        data={props.data}
        row-key="id"
        v-slots={{
          actions: ({ row }: { row: any }) => (
            <>
              <Collection
                isFavorite={row.isFavorite}
                onCollectionChange={(event: any) =>
                  onCollectionChange(row, event)
                }
                row={row}
              />
              <Share row={row} />
            </>
          ),
        }}
      />
    );
  },
});
