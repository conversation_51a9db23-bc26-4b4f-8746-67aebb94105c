import type { PropType } from 'vue';

import Collection from '#/components/collection/index.vue';
import Share from '#/components/share/index.vue';
import { LockOffIcon, LockOnIcon } from 'tdesign-icons-vue-next';
import { Tag as TTag, Tooltip as TTooltip } from 'tdesign-vue-next';
import { defineComponent, nextTick, onMounted, ref } from 'vue';

import styles from './index.module.less';

export default defineComponent({
  name: 'TopCardOfDataset',
  props: {
    data: {
      type: Object as PropType<any>,
      required: true,
    },
    rowData: {
      type: Object as PropType<any>,
      required: true,
    },
  },
  setup(props) {
    const { data, rowData } = props;
    // refs for cover and info
    const coverRef = ref<HTMLElement | null>(null);
    const infoRef = ref<HTMLElement | null>(null);
    const setCoverWidth = () => {
      nextTick(() => {
        if (infoRef.value && coverRef.value) {
          const height = infoRef.value.offsetHeight;
          coverRef.value.style.height = `${height}px`;
          coverRef.value.style.width = `${(height * 4) / 3}px`;
        }
      });
    };
    onMounted(() => {
      setCoverWidth();
      window.addEventListener('resize', setCoverWidth);
    });
    return () => (
      <div class={styles['top-card']}>
        <div class={styles['top-card-main']}>
          {/* 左侧图片区 */}
          <div class={styles['top-card-cover']} ref={coverRef}>
            <img
              src={data.value.imgUrl}
              style="width:100%;height:100%;object-fit:cover;"
            />
            {/* 右上角公开/私有图标 */}
            {rowData.value.dataAuth_text && (
              <div class={styles['top-card-public-icon']}>
                <TTooltip content={rowData.value.dataAuth_text} placement="top">
                  {rowData.value.dataAuth_text === '公开' ? (
                    <LockOffIcon size={'20px'} style={{ color: '#52c41a' }} />
                  ) : (
                    <LockOnIcon size={'20px'} style={{ color: '#faad14' }} />
                  )}
                </TTooltip>
              </div>
            )}
          </div>
          {/* 右侧信息区 */}
          <div class={styles['top-card-info']} ref={infoRef}>
            <div class={styles['top-card-title-row']}>
              <div class={styles['top-card-title']}>
                {rowData.value.data_name}
              </div>
              {rowData.value.extra && (
                <div class={styles['top-card-extra']}>{data.value.extra}</div>
              )}
            </div>
            <div class={styles['top-card-info-list']}>
              <div class={styles['top-card-info-item']}>
                <span class={styles['top-card-info-label']}>DOI</span>
                <span class={styles['top-card-info-value']}>
                  {data.value.doi || '-'}
                </span>
              </div>
              <div class={styles['top-card-info-item']}>
                <span class={styles['top-card-info-label']}>CSTR</span>
                <span class={styles['top-card-info-value']}>
                  {data.value.cstr || '-'}
                </span>
              </div>
              <div class={styles['top-card-info-item']}>
                <span class={styles['top-card-info-label']}>适用任务</span>
                <span class={styles['top-card-info-value']}>
                  {data.value.tasks || '-'}
                </span>
              </div>
              <div class={styles['top-card-info-format-row']}>
                <span class={styles['top-card-info-format']}>
                  <span class={styles['top-card-info-label']}>格式</span>
                  <span class={styles['top-card-info-value']}>
                    {data.value.formats || '-'}
                  </span>
                </span>
                <span class={styles['top-card-info-format']}>
                  <span class={styles['top-card-info-label']}>语言</span>
                  <span class={styles['top-card-info-value']}>
                    {data.value.languages || '-'}
                  </span>
                </span>
              </div>
              <div class={styles['top-card-info-item']}>
                <span class={styles['top-card-info-label']}>开源协议</span>
                <span class={styles['top-card-info-value']}>
                  {data.value.license || '-'}
                </span>
              </div>
              <div class={styles['top-card-info-item']}>
                <span class={styles['top-card-info-label']}>关键词</span>
                <span class={styles['top-card-info-value']}>
                  {data.value.keywords &&
                  typeof data.value.keywords === 'string' &&
                  data.value.keywords.trim() !== ''
                    ? data.value.keywords
                        .split(',')
                        .map((kw: string, idx: number) => (
                          <TTag
                            key={idx}
                            shape="round"
                            style="margin: 0 6px 4px 0;"
                            theme="primary"
                            variant="outline"
                          >
                            {kw.trim()}
                          </TTag>
                        ))
                    : '-'}
                </span>
              </div>
              {/* <div class={styles['top-card-meta']}>
                <a href="#" style="color: #7c3aed; text-decoration: underline;">
                  @liucong
                </a>
                <span style="margin: 0 8px;">提供</span>
                <span>4,246下载</span>
                <span style="margin: 0 8px;">676.65MB</span>
                <span>2025-02-21更新</span>
              </div> */}
            </div>
            {/* 操作按钮区 */}
            <div class={styles['top-card-actions']}>
              <span class={styles['top-card-action-item']}>
                浏览：{' '}
                {rowData.value.browsingCount ||
                  rowData.value.browsingCount ||
                  0}
              </span>
              <Collection isFavorite={rowData.value.isFavorite} row={rowData} />
              <Share row={rowData} />
              {rowData.value.actions &&
                (typeof rowData.value.actions === 'function'
                  ? rowData.value.actions()
                  : rowData.value.actions)}
            </div>
          </div>
        </div>
      </div>
    );
  },
});
