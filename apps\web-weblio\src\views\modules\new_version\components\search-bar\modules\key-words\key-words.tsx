import { getDictItems } from '#/api';
import { defineComponent, onMounted, ref } from 'vue';

import styles from './style.module.less';

export default defineComponent({
  name: 'KeyWords',
  emits: {
    change: (keyword: string) => !!keyword,
  },
  setup(_, { emit }) {
    const keywords = ref<{ label: string; value: any }[]>([]);

    onMounted(async () => {
      try {
        const res = await getDictItems('HOT_SEARCH');
        if (Array.isArray(res)) {
          keywords.value = res;
        }
      } catch (error) {
        console.error('Failed to fetch hot keywords:', error);
        keywords.value = []; // Fallback to an empty array on error
      }
    });

    const handleClick = (keyword: { label: string; value: any }) => {
      emit('change', keyword.label);
    };

    return () => (
      <div class={styles.container}>
        <span class={styles.label}>热门搜索：</span>
        <div class={styles.tagsWrapper}>
          {keywords.value.map((keyword) => (
            <span
              class={styles.tag}
              key={keyword.value}
              onClick={() => handleClick(keyword)}
            >
              {keyword.label}
            </span>
          ))}
        </div>
      </div>
    );
  },
});
