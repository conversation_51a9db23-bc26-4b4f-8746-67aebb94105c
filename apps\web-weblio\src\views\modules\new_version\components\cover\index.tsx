import type { PropType } from 'vue';

import { defineComponent } from 'vue';

import styles from './style.module.less';

interface CoverData {
  img?: string;
  dataName: string;
  author?: string;
  dataType_text?: string;
}

export default defineComponent({
  name: 'Cover',
  props: {
    data: {
      type: Object as PropType<CoverData>,
      default: () => ({
        dataName: '高等学校教学用书',
        author: '罗曼科夫',
        dataType_text: '教材',
      }),
    },
  },
  setup(props) {
    return () => {
      if (props.data?.img) {
        return (
          <img
            alt={props.data.dataName}
            class={styles.imageCover}
            src={props.data.img}
          />
        );
      }

      const { data } = props;

      return (
        <div class={styles.container}>
          <div class={styles.main}>
            <h1 class={styles.title}>{data.dataName}</h1>
            <p class={styles.author}>{data.author}</p>
            <div class={styles.decoration}>
              <span class={styles.line}></span>
              <span class={styles.flourish}>❦</span>
              <span class={styles.line}></span>
            </div>
          </div>
          <div class={styles.footer}>
            <p>{data.dataType_text}</p>
          </div>
        </div>
      );
    };
  },
});
