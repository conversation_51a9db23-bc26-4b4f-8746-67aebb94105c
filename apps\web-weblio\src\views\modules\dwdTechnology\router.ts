import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ic:baseline-apps',
      keepAlive: true,
      order: 1,
      title: '工艺一览',
    },
    name: 'dwdTechnology',
    path: '/dwdTechnology',
    children: [
      {
        meta: {
          title: '工艺一览-详情',
        },
        name: 'dwdTechnologyIndex',
        path: '/dwdTechnology/index',
        component: () =>
          import('#/views/modules/dwdTechnology/index.vue'),
      },
    ],
  },
];

export default routes;
