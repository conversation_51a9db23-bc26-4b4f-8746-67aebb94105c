import { defineComponent } from 'vue';

import styles from './style.module.less';

export default defineComponent({
  name: 'SearchInput',
  props: {
    value: {
      type: String,
      default: '',
    },
  },
  emits: ['update:value', 'search', 'aiSearch'],
  setup(props, { emit }) {
    return () => (
      <div class={styles.container}>
        <input
          class={styles.input}
          onInput={(e) => {
            emit('update:value', (e.target as HTMLInputElement).value);
          }}
          placeholder="请输入关键词"
          type="text"
          value={props.value}
        />
        <div class={styles.buttons}>
          <button
            class={[styles.button, styles.qaButton]}
            onClick={() => emit('aiSearch')}
          >
            <img alt="AI" src="/static/home-page/icons/ai-icon.png" />
            <span>问答</span>
          </button>
          <button
            class={[styles.button, styles.searchButton]}
            onClick={() => emit('search')}
          >
            <img alt="Search" src="/static/home-page/icons/search-icon.png" />
            <span>检索</span>
          </button>
        </div>
      </div>
    );
  },
});
