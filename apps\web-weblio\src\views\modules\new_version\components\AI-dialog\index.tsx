import { Dialog } from 'tdesign-vue-next';
import { defineComponent } from 'vue';

import { useAiAnswerModal } from '../../hooks';

export default defineComponent({
  name: 'AIDialog',
  setup() {
    const { isVisible, closeModal } = useAiAnswerModal();
    return () => (
      <Dialog
        header="AI问答"
        onClose={closeModal}
        v-model:visible={isVisible.value}
      ></Dialog>
    );
  },
});
