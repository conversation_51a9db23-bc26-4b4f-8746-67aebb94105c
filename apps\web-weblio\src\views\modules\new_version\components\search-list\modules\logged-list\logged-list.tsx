import type { PropType } from 'vue';

import SearchCardList from '#/views/modules/new_version/components/search-list/modules/card-list/card-list';
import SearchTableList from '#/views/modules/new_version/components/search-list/modules/table-list/table-list';
import { Empty, Loading, Pagination } from 'tdesign-vue-next';
import { defineComponent } from 'vue';

import styles from './logged-list.module.less';

export default defineComponent({
  name: 'LoggedList',
  props: {
    loading: {
      type: Boolean,
      required: true,
    },
    listData: {
      type: Array as PropType<any[]>,
      required: true,
    },
    viewType: {
      type: String as PropType<'card' | 'list'>,
      required: true,
    },
    columns: {
      type: Array as PropType<any[]>,
      required: true,
    },
    currentPage: {
      type: Number,
      required: true,
    },
    pageSize: {
      type: Number,
      required: true,
    },
    total: {
      type: Number,
      required: true,
    },
  },
  emits: [
    'collectionChange',
    'itemClick',
    'pageChange',
    'pageSizeChange',
    'update:currentPage',
    'update:pageSize',
  ],
  setup(props, { emit }) {
    const onCollectionChange = (item: any, event: any) => {
      emit('collectionChange', item, event);
    };

    const onItemClick = (item: any) => {
      emit('itemClick', item);
    };

    const onPageChange = (pageInfo: { current: number; pageSize: number }) => {
      emit('update:currentPage', pageInfo.current);
      emit('pageChange', pageInfo);
    };

    const onPageSizeChange = (pageSize: number) => {
      emit('update:pageSize', pageSize);
      emit('pageSizeChange', pageSize);
    };

    return () => (
      <>
        {props.loading ? (
          <Loading
            class={styles['loading-indicator']}
            loading={props.loading}
            text="加载中..."
          />
        ) : props.listData.length > 0 ? (
          <>
            <div class={styles['table-container']}>
              {props.viewType === 'list' ? (
                <SearchTableList
                  columns={props.columns}
                  data={props.listData}
                  onCollectionChange={onCollectionChange}
                />
              ) : (
                <SearchCardList
                  data={props.listData}
                  onCollectionChange={onCollectionChange}
                  onItemClick={onItemClick}
                />
              )}
            </div>
            <Pagination
              class={styles.pagination}
              modelValue={props.currentPage}
              onChange={onPageChange}
              onPageSizeChange={onPageSizeChange}
              pageSize={props.pageSize}
              show-jumper
              total={props.total}
            />
          </>
        ) : (
          <Empty class={styles['empty-indicator']}>暂无数据</Empty>
        )}
      </>
    );
  },
});
