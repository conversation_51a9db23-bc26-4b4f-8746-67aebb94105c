<script setup lang="tsx">
import type { TreeNodeValue } from 'tdesign-vue-next';

import { getDictItems } from '#/api';
import { useSearchStore } from '#/store/search';
import HomeSearch from '#/views/modules/new_version/components/home-search/index.tsx';
import SearchCategoryMenu from '#/views/modules/new_version/components/search-category-menu/index.tsx';
import SearchConditions from '#/views/modules/new_version/components/search-conditions/index.tsx';
import SearchListHead from '#/views/modules/new_version/components/search-list/modules/head/head';
import LoggedList from '#/views/modules/new_version/components/search-list/modules/logged-list/logged-list';
import { usePermissions } from '#/views/modules/new_version/hooks/usePermissions';
import { computed, onMounted, onUnmounted, ref } from 'vue';

import { getCollectByClassCode, getSearchData } from './api';
// 定义 ListItem 类型，确保 listData 类型安全
interface ListItem {
  id: number | string;
  // 基本信息
  dataName?: string; // 数据名称
  dataImg?: string; // 数据图片
  dataDescri?: string; // 数据描述
  author?: string; // 作者
  cas?: string; // CAS号
  molecularFormula?: string; // 分子式
  categoryName?: string; // 分类名称
  browsingCount?: number; // 浏览次数
  baseCode?: string; // 基础代码，用于详情和下载
  fileType?: string; // 文件类型

  // 时间和状态
  createTime: string;
  isFavorite: boolean;
  isVisible?: number | string; // 审批状态：1-审批通过，0-待审批

  // 标签和分类
  tags: string[];
  labelName?: string; // 标签名称（逗号分隔）

  // 数据类型和权限
  dataType_text?: string;
  dataAuth_text?: string;
  dataType?: number | string;
  dataAuth?: number | string;
}

const searchStore = useSearchStore();
const columns = ref<any[]>([
  {
    colKey: 'data_name',
    title: '名称',
  },
  {
    colKey: 'dataType',
    title: '资源类型',
  },
  {
    colKey: 'dataAuth',
    title: '共享方式',
  },
  {
    colKey: 'createTime',
    title: '发布时间',
  },
  {
    colKey: 'categoryName',
    title: '所属分类',
  },
  {
    colKey: 'actions',
    title: '操作',
  },
]);
const viewType = ref<'card' | 'list'>('card');
// 右侧条件区数据（提前声明，指定类型）
const shareTypeOptions = ref<
  { checkAll?: boolean; label: string; value: string }[]
>([]); // 共享方式
const yearOptions = ref<{ label: string; value: string }[]>([]); // 年份
const keywordOptions = ref<
  { count?: number; label: string; theme?: string; value: string }[]
>([]); // 关键词

const documentTypeOptions = ref<{ label: string; value: string }[]>([]); // 资源类型
const selectedShareTypes = ref<string[]>(['']); // 共享方式
const selectedYears = ref<string[]>([]); // 年份
const selectedKeywords = ref<string[]>([]); // 关键词
const selectedDocumentTypes = ref<string>('0'); // 资源类型

// 选中的树节点
const checkedKeys = ref<TreeNodeValue[]>([]);
// 展开的树节点
const expandedKeys = ref<TreeNodeValue[]>([]);

// 搜索相关数据
const listData = ref<ListItem[]>([]);
const total = ref(0);
const pageSize = ref(20);
const currentPage = ref(1);
const sortType = ref<'count' | 'date' | 'default'>('default');
const loading = ref(false);
const treeLoading = ref(false);

// 执行搜索（静态结果，之后替换成API）
const performSearch = async () => {
  loading.value = true;
  const params = {
    current: currentPage.value,
    pageSize: pageSize.value,
    category: checkedKeys.value, // 分类
    searchContent: searchStore.searchQuery, // 搜索内容
    queryType: selectedDocumentTypes.value, // 资源类型
    dataAuthList: selectedShareTypes.value.filter((el) => !!el), // 共享
    years: selectedYears.value.filter((el) => !el.includes('_before')), // 年份
    yearBefore: selectedYears.value.filter((el) => el.includes('_before')), // 年份
    labelCode: selectedKeywords.value?.[0] || '', // 关键词
    esIndex: import.meta.env.VITE_ES_INDEX, // 索引
    sortType: sortType.value,
    logicList: searchStore.logicList,
  };
  try {
    const res = await getSearchData(params);
    if (res) {
      listData.value = res.page.records;
      total.value = res.page.total;
      // 重写关键词
      res.buckets && (keywordOptions.value = res.buckets);
    }
    loading.value = false;
  } catch (error) {
    console.error('搜索失败', error);
  } finally {
    loading.value = false;
  }
};

const { handleResourceAccess: handleItemClick } = usePermissions(performSearch);

// 左侧树形结构数据
const treeData = ref();

// 递归查找目标节点，并收集其所有子节点的 classifyCode
function findAllClassifyCodes(tree: any[], targetCode: string): string[] {
  // 递归查找目标节点
  function findNode(nodes: any[]): any | null {
    for (const node of nodes) {
      if (`${node.classifyCode}` === targetCode) {
        return node;
      }
      if (node.children) {
        const found = findNode(node.children);
        if (found) return found;
      }
    }
    return null;
  }

  // 递归收集所有 classifyCode
  function collectCodes(node: any): string[] {
    let codes = [node.classifyCode];
    if (node.children && node.children.length > 0) {
      for (const child of node.children) {
        codes = [...codes, ...collectCodes(child)];
      }
    }
    return codes;
  }

  const targetNode = findNode(tree);
  if (!targetNode) return [];
  return collectCodes(targetNode);
}

// 获取树的前两层节点的keys
function getFirstTwoLevelsKeys(tree: any[], currentLevel = 1): string[] {
  if (!tree || currentLevel > 2) return [];

  const keys: string[] = [];
  for (const node of tree) {
    if (currentLevel < 2) {
      keys.push(node.classifyCode);
      if (node.children && node.children.length > 0) {
        keys.push(...getFirstTwoLevelsKeys(node.children, currentLevel + 1));
      }
    }
  }
  return keys;
}

// 初始化左侧树结构数据
const initTreeData = async () => {
  treeLoading.value = true;
  try {
    // 这里 params 可根据实际需求调整
    const params = {};
    const res = await getCollectByClassCode(params);
    if (res && Array.isArray(res)) {
      treeData.value = res;

      // 设置默认展开前两层节点
      try {
        expandedKeys.value = getFirstTwoLevelsKeys(treeData.value);
      } catch (expandError) {
        console.error('设置展开节点失败:', expandError);
        expandedKeys.value = [];
      }

      // 处理初始分类选择
      if (searchStore.initCategory !== undefined) {
        try {
          // 递归查找并收集所有节点的 classifyCode
          checkedKeys.value = findAllClassifyCodes(
            treeData.value,
            String(searchStore.initCategory),
          );
        } catch (categoryError) {
          console.error('设置初始分类失败:', categoryError);
          checkedKeys.value = [];
        }
      }
    } else {
      console.warn('获取到的树结构数据格式不正确');
      treeData.value = [];
    }
  } catch (error) {
    console.error('获取左侧树结构失败:', error);
    // 设置默认值确保页面正常工作
    treeData.value = [];
    expandedKeys.value = [];
    checkedKeys.value = [];
  } finally {
    treeLoading.value = false;
  }
};
// 初始化字典数据
const initDictData = async () => {
  try {
    // 共享方式
    try {
      const sharingMethod = await getDictItems('SHARING_METHOD');
      shareTypeOptions.value = [...sharingMethod];
    } catch (error) {
      console.error('获取共享方式字典失败:', error);
      // 设置默认值确保页面正常工作
      shareTypeOptions.value = [{ label: '全选', value: '', checkAll: true }];
    }
    // 资源类型
    try {
      const docType = await getDictItems('DOCUMENT_TYPE');
      documentTypeOptions.value = [...docType];
    } catch (error) {
      console.error('获取资源类型字典失败:', error);
      documentTypeOptions.value = [];
    }
  } catch (error) {
    console.error('初始化字典数据时发生未知错误:', error);
  }
};
// 初始化年份
const initYearOptions = () => {
  const currentYear = new Date().getFullYear();
  const years: { label: string; value: string }[] = [];
  for (let i = 0; i < 5; i++) {
    const y = (currentYear - i).toString();
    years.push({ label: `${y}年`, value: y });
  }
  years.push({
    label: `${currentYear - 5}年及更早`,
    value: `${currentYear - 5}_before`,
  });
  yearOptions.value = years;
};

// 统一检索入口
function handleSearch() {
  currentPage.value = 1;
  performSearch();
}

// 资源类型（树）变化
function handleResourceTypeChange() {
  currentPage.value = 1;
  performSearch();
}

// 排序变化
function handleSortChange() {
  currentPage.value = 1;
  performSearch();
}

// 分页变化
function handlePageChange(pageInfo: { current: number; pageSize: number }) {
  currentPage.value = pageInfo.current;
  pageSize.value = pageInfo.pageSize;
  performSearch();
}

function handlePageSizeChange(newPageSize: number) {
  pageSize.value = newPageSize;
  currentPage.value = 1;
  performSearch();
}

// 关键词点击逻辑已移入 SearchConditions 组件

const sortOptions = ref([
  { label: '默认排序', value: 'default' },
  { label: '发布时间', value: 'date' },
  { label: '热度', value: 'count' },
]);
// 计算属性：年份是否禁用
const isYearDisabled = computed(() => {
  return false;
});

// 共享方式全选逻辑
function handleShareTypeChange(_val: string[]) {
  // 触发搜索
  currentPage.value = 1;
  performSearch();
}

function onCollectionChange(
  item: any,
  event: { isFavorite: boolean; row: any },
) {
  const target = listData.value.find((d) => d.id === item.id);
  if (target) {
    target.isFavorite = event.isFavorite;
  }
}

// 组件挂载时初始化
onMounted(() => {
  initTreeData();
  initDictData();
  initYearOptions();
  performSearch();
});

// 组件卸载时，清除Pinia中的分类
onUnmounted(() => {
  searchStore.setInitCategory(undefined);
});
</script>

<template>
  <div class="search-page-container">
    <!-- 顶部搜索栏 -->
    <HomeSearch class="search-box-top" />

    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 左侧树形结构 -->
      <SearchCategoryMenu
        :data="treeData"
        :loading="treeLoading"
        v-model:checked="checkedKeys"
        v-model:expanded="expandedKeys"
        @change="handleResourceTypeChange"
      />

      <!-- 右侧内容区 -->
      <div class="right-content">
        <!-- 条件筛选栏 -->
        <SearchConditions
          v-model:selected-share-types="selectedShareTypes"
          v-model:selected-years="selectedYears"
          v-model:selected-keywords="selectedKeywords"
          v-model:selected-document-types="selectedDocumentTypes"
          :share-type-options="shareTypeOptions"
          :document-type-options="documentTypeOptions"
          :year-options="yearOptions"
          :keyword-options="keywordOptions"
          :is-year-disabled="isYearDisabled"
          @share-type-change="handleShareTypeChange"
          @search="handleSearch"
        />

        <!-- 结果列表 -->
        <div class="list-bar">
          <SearchListHead
            v-model:sort-type="sortType"
            v-model:view-type="viewType"
            :total="total"
            :sort-options="sortOptions"
            @sort-change="handleSortChange"
          />

          <LoggedList
            :loading="loading"
            :list-data="listData"
            :view-type="viewType"
            :columns="columns"
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            @collection-change="onCollectionChange"
            @item-click="handleItemClick"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.search-page-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
  background-color: #f5f5f5;

  .search-box-top {
    margin-bottom: 20px;
  }

  .main-content {
    display: flex;
    flex: 1;
    min-height: 0;
  }

  .right-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-left: 20px;
    min-width: 0;
  }

  .condition-bar {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 20px;

    .condition-row {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }

      .condition-label {
        width: 80px;
        font-weight: bold;
        color: #333;
      }

      .condition-group {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
      }
    }
  }

  .list-bar {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    min-height: 0;
  }

  .table-container {
    flex: 1;
    overflow: auto;
    min-height: 0;
  }

  .card-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }

  .pagination {
    margin-top: 20px;
    text-align: right;
  }

  .loading-indicator,
  .empty-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }
}
</style>
