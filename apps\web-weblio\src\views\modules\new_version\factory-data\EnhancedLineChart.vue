<template>
  <div class="enhanced-line-chart">
    <div ref="chartContainer" :style="{ height: `${height}px`, width: '100%' }"></div>
  </div>
</template>

<script setup lang="ts">
import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import * as echarts from 'echarts'

// Props 定义
interface Props {
  data: any[]
  title?: string
  height?: number
  xAxisKey?: string
  yAxisKey?: string
  seriesKey?: string
  showLegend?: boolean
  smooth?: boolean
  theme?: 'light' | 'dark'
}

const props = withDefaults(defineProps<Props>(), {
  title: '数据趋势图',
  height: 400,
  xAxisKey: 'Date',
  yAxisKey: 'Price',
  seriesKey: 'Name',
  showLegend: true,
  smooth: true,
  theme: 'light'
})

// 响应式数据
const chartContainer = ref<HTMLDivElement>()
let chartInstance: echarts.ECharts | null = null

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return

  chartInstance = echarts.init(chartContainer.value)
  updateChart()
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance || !props.data.length) return

  // 处理数据，按系列分组
  const seriesMap = new Map<string, any[]>()
  const xAxisData = new Set<string>()

  props.data.forEach(item => {
    const seriesName = item[props.seriesKey] || '默认系列'
    const xValue = item[props.xAxisKey]
    const yValue = item[props.yAxisKey]

    if (!seriesMap.has(seriesName)) {
      seriesMap.set(seriesName, [])
    }
    
    seriesMap.get(seriesName)?.push({
      name: xValue,
      value: [xValue, yValue],
      originalData: item
    })
    
    xAxisData.add(xValue)
  })

  // 转换为 ECharts 需要的格式
  const series = Array.from(seriesMap.entries()).map(([name, data], index) => {
    const colors = [
      '#4285f4', '#ea4335', '#fbbc04', '#34a853', '#9aa0a6', 
      '#ff6d01', '#9c27b0', '#00bcd4', '#795548', '#607d8b'
    ]
    
    return {
      name,
      type: 'line',
      smooth: props.smooth,
      symbol: 'none', // 默认不显示点
      symbolSize: 8,
      lineStyle: {
        width: 3,
        color: colors[index % colors.length]
      },
      itemStyle: {
        color: colors[index % colors.length],
        borderColor: '#fff',
        borderWidth: 2
      },
      data: data.map(item => [item.value[0], item.value[1]]),
      emphasis: {
        focus: 'series',
        lineStyle: {
          width: 4
        },
        itemStyle: {
          shadowBlur: 10,
          shadowColor: colors[index % colors.length]
        }
      },
      // 悬浮时显示点
      showSymbol: false,
      hoverAnimation: true
    }
  })

  const option = {
    title: {
      text: props.title,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: props.theme === 'dark' ? '#fff' : '#333'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line',
        lineStyle: {
          color: '#ddd',
          width: 1,
          type: 'solid'
        },
        label: {
          backgroundColor: '#6a7985'
        }
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e6e6e6',
      borderWidth: 1,
      textStyle: {
        color: '#333',
        fontSize: 12
      },
      padding: [8, 12],
      formatter: (params: any) => {
        let result = `<div style="font-weight: bold; margin-bottom: 4px;">${params[0].axisValue}</div>`
        params.forEach((param: any) => {
          result += `<div style="margin: 2px 0;">
            <span style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background-color: ${param.color}; margin-right: 6px;"></span>
            ${param.seriesName}: <span style="font-weight: bold;">${param.value[1]}</span>
          </div>`
        })
        return result
      },
      // 悬浮时显示数据点
      triggerOn: 'mousemove',
      enterable: false
    },
    legend: {
      show: props.showLegend,
      top: 30,
      textStyle: {
        color: props.theme === 'dark' ? '#fff' : '#333'
      }
    },
    grid: {
      left: '2%',
      right: '3%',
      bottom: '8%',
      top: props.showLegend ? '12%' : '8%',
      containLabel: true
    },
    toolbox: {
      feature: {
        saveAsImage: {
          title: '保存为图片'
        },
        dataZoom: {
          title: {
            zoom: '区域缩放',
            back: '区域缩放还原'
          }
        },
        restore: {
          title: '还原'
        }
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      axisLabel: {
        color: props.theme === 'dark' ? '#fff' : '#666',
        fontSize: 11,
        // 智能间隔显示标签，避免过于密集
        interval: 'auto',
        rotate: 0,
        formatter: (value: string) => {
          // 如果是日期格式，可以进行格式化
          if (value.includes('-')) {
            const date = new Date(value)
            if (!isNaN(date.getTime())) {
              return `${date.getMonth() + 1}/${date.getDate()}`
            }
          }
          return value
        }
      },
      axisLine: {
        lineStyle: {
          color: props.theme === 'dark' ? '#444' : '#e6e6e6'
        }
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: props.theme === 'dark' ? '#fff' : '#666',
        fontSize: 11
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: props.theme === 'dark' ? '#333' : '#f0f0f0',
          type: 'solid'
        }
      }
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      // {
      //   start: 0,
      //   end: 100,
      //   height: 30,
      //   bottom: 20
      // }
    ],
    series
  }

  chartInstance.setOption(option, true)
  
  // 添加鼠标悬浮事件，显示数据点
  chartInstance.off('mouseover')
  chartInstance.off('mouseout')
  
  chartInstance.on('mouseover', 'series', (params: any) => {
    const seriesIndex = params.seriesIndex
    const updatedSeries = [...series]
    updatedSeries[seriesIndex] = {
      ...updatedSeries[seriesIndex],
      showSymbol: true,
      symbol: 'circle'
    }
    
    chartInstance.setOption({
      series: updatedSeries
    })
  })
  
  chartInstance.on('mouseout', 'series', () => {
    const updatedSeries = series.map(s => ({
      ...s,
      showSymbol: false
    }))
    
    chartInstance.setOption({
      series: updatedSeries
    })
  })
}

// 响应式调整图表大小
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 监听数据变化
watch(() => props.data, () => {
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

// 监听主题变化
watch(() => props.theme, () => {
  nextTick(() => {
    updateChart()
  })
})

// 生命周期
onMounted(() => {
  nextTick(() => {
    initChart()
    window.addEventListener('resize', handleResize)
  })
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})

// 暴露方法
defineExpose({
  getChartInstance: () => chartInstance,
  updateChart,
  resize: handleResize
})
</script>

<style scoped lang="scss">
.enhanced-line-chart {
  width: 100%;
  position: relative;
  
  &:hover {
    .chart-actions {
      opacity: 1;
    }
  }
}

.chart-actions {
  position: absolute;
  top: 10px;
  right: 10px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 10;
  
  .action-btn {
    margin-left: 8px;
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #ddd;
    cursor: pointer;
    
    &:hover {
      background: #f0f0f0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .enhanced-line-chart {
    :deep(.echarts-tooltip) {
      font-size: 12px;
    }
  }
}
</style>
